[{"default_value": null, "doc_type": "Guardian", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "naming_series", "modified": "2020-07-27 10:55:17.466008", "name": "Guardian-naming_series-default", "parent": null, "parentfield": null, "parenttype": null, "property": "default", "property_type": "Text", "row_name": null, "value": "EDU-G-.YYYY.-"}, {"default_value": null, "doc_type": "Fee Structure", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "DocType", "field_name": null, "modified": "2020-07-31 12:20:26.094773", "name": "Fee Structure-search_fields", "parent": null, "parentfield": null, "parenttype": null, "property": "search_fields", "property_type": "Data", "row_name": null, "value": "program, default_fee_category, academic_year,company"}, {"default_value": null, "doc_type": "Fee Structure", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "DocType", "field_name": null, "modified": "2020-07-31 12:26:12.111511", "name": "Fee Structure-title_field", "parent": null, "parentfield": null, "parenttype": null, "property": "title_field", "property_type": "Data", "row_name": null, "value": "default_fee_category"}, {"default_value": null, "doc_type": "Program Fee", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "student_category", "modified": "2020-07-31 12:30:14.254443", "name": "Program Fee-student_category-fetch_from", "parent": null, "parentfield": null, "parenttype": null, "property": "fetch_from", "property_type": "Small Text", "row_name": null, "value": "fee_structure.student_category"}, {"default_value": null, "doc_type": "Program Fee", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "due_date", "modified": "2020-07-31 12:57:20.627478", "name": "Program Fee-due_date-columns", "parent": null, "parentfield": null, "parenttype": null, "property": "columns", "property_type": "Int", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Program Fee", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "student_category", "modified": "2020-07-31 12:57:38.633384", "name": "Program Fee-student_category-columns", "parent": null, "parentfield": null, "parenttype": null, "property": "columns", "property_type": "Int", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Program Enrollment", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "section_break_7", "modified": "2020-09-21 16:08:35.059526", "name": "Program Enrollment-section_break_7-collapsible", "parent": null, "parentfield": null, "parenttype": null, "property": "collapsible", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Program Enrollment", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "enrolled_courses", "modified": "2020-09-21 16:08:53.548779", "name": "Program Enrollment-enrolled_courses-collapsible", "parent": null, "parentfield": null, "parenttype": null, "property": "collapsible", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Guardian", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "students", "modified": "2021-03-13 15:19:09.919026", "name": "Guardian-students-read_only", "parent": null, "parentfield": null, "parenttype": null, "property": "read_only", "property_type": "Check", "row_name": null, "value": "0"}, {"default_value": null, "doc_type": "Program", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "program_fee", "modified": "2021-01-28 18:27:54.254801", "name": "Program-program_fee-allow_bulk_edit", "parent": null, "parentfield": null, "parenttype": null, "property": "allow_bulk_edit", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Fees", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "DocType", "field_name": null, "modified": "2021-05-02 19:24:47.510173", "name": "Fees-main-track_changes", "parent": null, "parentfield": null, "parenttype": null, "property": "track_changes", "property_type": "Check", "row_name": null, "value": "1"}, {"default_value": null, "doc_type": "Fees", "docstatus": 0, "doctype": "Property Setter", "doctype_or_field": "<PERSON><PERSON><PERSON>", "field_name": "letter_head", "modified": "2021-05-05 07:20:23.195185", "name": "Fees-letter_head-fetch_from", "parent": null, "parentfield": null, "parenttype": null, "property": "fetch_from", "property_type": "Small Text", "row_name": null, "value": "company.default_letter_head"}]