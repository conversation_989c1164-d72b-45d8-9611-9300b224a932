# Copyright (c) 2024, <PERSON> and contributors
# For license information, please see license.txt

import frappe
from frappe.model.document import Document
from frappe import _

class ClearingCharges(Document):
    def before_save(self):
        self.fetch_total_charges()

    def fetch_total_charges(self):
        # Initialize total variables
        tra_total = 0
        port_total = 0
        shipment_total = 0
        physical_total = 0
        
        # Fetch charges from the child table
        for charge in self.charges:
            if charge.charge_type == "TRA Clearance":
                tra_total += float(charge.amount)
            elif charge.charge_type == "Port Clearance":
                port_total += float(charge.amount)
            elif charge.charge_type == "Shipping Line Clearance":
                shipment_total += float(charge.amount)
            elif charge.charge_type == "Physical Verification":
                physical_total += float(charge.amount)
        
        # Update the doc with fetched totals
        self.tra_clearance_total = tra_total
        self.port_clearance_total = port_total
        self.shipment_clearance_total = shipment_total
        self.physical_clearance_total = physical_total
        
        # Calculate total sum
        self.total_charges_sum = tra_total + port_total + shipment_total + physical_total


@frappe.whitelist()
def create_debit_note(clearing_charges_name):
    """Create a Sales Invoice first, then create a Debit Note referencing it"""
    clearing_charges = frappe.get_doc("Clearing Charges", clearing_charges_name)

    if not clearing_charges.charges:
        frappe.throw(_("No charges found to create debit note"))

    # Step 1: Create the original Sales Invoice first
    original_invoice = create_original_invoice(clearing_charges)

    # Step 2: Create Debit Note referencing the original invoice
    debit_note = frappe.new_doc("Sales Invoice")
    debit_note.customer = clearing_charges.consigee
    debit_note.is_debit_note = 1  # Mark as debit note
    debit_note.return_against = original_invoice.name  # Reference the original invoice
    debit_note.posting_date = frappe.utils.nowdate()

    # Add items from charges with negative quantities for debit note
    for charge in clearing_charges.charges:
        # Ensure the charge type item exists for sales
        ensure_charge_item_exists_for_sales(charge.charge_type)

        debit_note.append("items", {
            "item_code": charge.charge_type,
            "qty": -1,  # Negative quantity for debit note
            "rate": charge.amount,
            "amount": -charge.amount  # Negative amount for debit note
        })

    # Set missing values to populate default accounts
    debit_note.set_missing_values()
    debit_note.save()
    return debit_note


def create_original_invoice(clearing_charges):
    """Create the original Sales Invoice that will be referenced by the debit note"""
    original_invoice = frappe.new_doc("Sales Invoice")
    original_invoice.customer = clearing_charges.consigee
    original_invoice.posting_date = frappe.utils.nowdate()

    # Add items from charges
    for charge in clearing_charges.charges:
        # Ensure the charge type item exists for sales
        ensure_charge_item_exists_for_sales(charge.charge_type)

        original_invoice.append("items", {
            "item_code": charge.charge_type,
            "qty": 1,
            "rate": charge.amount,
            "amount": charge.amount
        })

    # Set missing values and save
    original_invoice.set_missing_values()
    original_invoice.save()
    original_invoice.submit()  # Submit the original invoice

    return original_invoice


@frappe.whitelist()
def create_service_invoice(clearing_charges_name):
    """Create a Sales Invoice for clearing services only"""
    clearing_charges = frappe.get_doc("Clearing Charges", clearing_charges_name)

    # Create Sales Invoice for services
    service_invoice = frappe.new_doc("Sales Invoice")
    service_invoice.customer = clearing_charges.consigee
    service_invoice.posting_date = frappe.utils.nowdate()

    # Add clearing service items (you can customize this based on your service items)
    service_items = get_clearing_service_items(clearing_charges.clearing_file)

    for item in service_items:
        service_invoice.append("items", {
            "item_code": item.get("item_code"),
            "qty": item.get("qty", 1),
            "rate": item.get("rate"),
            "amount": item.get("amount")
        })

    if not service_invoice.items:
        frappe.throw(_("No clearing service items found to create invoice"))

    service_invoice.save()
    return service_invoice


# Removed supplier and expense account functions as we're now using Sales Invoice for debit notes


def get_clearing_service_items(clearing_file):
    """Get service items for the clearing file"""
    # This function should return the service items based on your business logic
    # For now, returning a basic service item

    # You can customize this to fetch actual service items from your system
    service_items = []

    # Ensure the clearing service item exists
    ensure_clearing_service_item_exists()

    # Example: Add a standard clearing service item
    # You can modify this to calculate rates based on clearing file details
    # The clearing_file parameter can be used to customize service rates/items
    base_rate = 100000  # Default rate, can be customized based on clearing_file

    service_items.append({
        "item_code": "Clearing Service",
        "qty": 1,
        "rate": base_rate,
        "amount": base_rate
    })

    return service_items


def ensure_clearing_service_item_exists():
    """Ensure that the Clearing Service item exists"""
    if not frappe.db.exists("Item", "Clearing Service"):
        # Check if "Services" item group exists, if not use "All Item Groups"
        item_group = "Services" if frappe.db.exists("Item Group", "Services") else "All Item Groups"

        item = frappe.new_doc("Item")
        item.item_code = "Clearing Service"
        item.item_name = "Clearing Service"
        item.item_group = item_group
        item.is_stock_item = 0
        item.is_sales_item = 1
        item.is_purchase_item = 0
        item.save()


def ensure_charge_item_exists(charge_type):
    """Ensure that the charge type item exists for purchase"""
    if not frappe.db.exists("Item", charge_type):
        # Check if "Services" item group exists, if not use "All Item Groups"
        item_group = "Services" if frappe.db.exists("Item Group", "Services") else "All Item Groups"

        item = frappe.new_doc("Item")
        item.item_code = charge_type
        item.item_name = charge_type
        item.item_group = item_group
        item.is_stock_item = 0
        item.is_sales_item = 0
        item.is_purchase_item = 1  # For purchase invoice
        item.save()


def ensure_charge_item_exists_for_sales(charge_type):
    """Ensure that the charge type item exists for sales/debit note"""
    if not frappe.db.exists("Item", charge_type):
        # Check if "Services" item group exists, if not use "All Item Groups"
        item_group = "Services" if frappe.db.exists("Item Group", "Services") else "All Item Groups"

        item = frappe.new_doc("Item")
        item.item_code = charge_type
        item.item_name = charge_type
        item.item_group = item_group
        item.is_stock_item = 0
        item.is_sales_item = 1  # For sales invoice/debit note
        item.is_purchase_item = 0
        item.save()
    else:
        # Update existing item to be sales item if it's not already
        item = frappe.get_doc("Item", charge_type)
        if not item.is_sales_item:
            item.is_sales_item = 1
            item.save()
