{"actions": [], "allow_rename": 1, "autoname": "format:CC-{clearing_file}-{#####}", "creation": "2024-07-15 23:20:46.142648", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["consigee_details_section", "clearing_file", "column_break_ahtow", "status", "consigee", "service_charge_details_section", "charges", "section_break_0qrth", "column_break_wv5mn", "column_break_fp5jw", "tra_clearance_total", "port_clearance_total", "shipment_clearance_total", "physical_clearance_total", "total_charges_sum", "column_break_1", "invoice_number", "debit_note_number", "column_break_hqjie", "generate_debit_note", "column_break_service", "generate_service_invoice"], "fields": [{"fieldname": "service_charge_details_section", "fieldtype": "Section Break", "label": "Charge Details"}, {"fieldname": "column_break_1", "fieldtype": "Section Break", "label": "Payment Details"}, {"fieldname": "invoice_number", "fieldtype": "Link", "in_list_view": 1, "label": "Service Invoice", "options": "Sales Invoice", "read_only": 1}, {"fieldname": "debit_note_number", "fieldtype": "Link", "in_list_view": 1, "label": "Debit Note Invoice", "options": "Purchase Invoice", "read_only": 1}, {"fieldname": "consigee_details_section", "fieldtype": "Section Break", "label": "Consigee Details"}, {"fieldname": "clearing_file", "fieldtype": "Link", "in_list_view": 1, "label": "Clearing File", "options": "Clearing File", "reqd": 1}, {"fieldname": "column_break_ahtow", "fieldtype": "Column Break"}, {"fetch_from": "clearing_file.customer", "fieldname": "consigee", "fieldtype": "Link", "in_list_view": 1, "label": "Consigee", "options": "Customer", "read_only": 1}, {"fieldname": "charges", "fieldtype": "Table", "label": "Charges", "options": "Clearing Charge Detail", "reqd": 1}, {"fieldname": "status", "fieldtype": "Select", "in_list_view": 1, "label": "Payment Status", "options": "Unpaid\nPartially Paid\nPaid", "reqd": 1}, {"fieldname": "generate_debit_note", "fieldtype": "<PERSON><PERSON>", "label": "Generate Debit Note"}, {"fieldname": "column_break_service", "fieldtype": "Column Break"}, {"fieldname": "generate_service_invoice", "fieldtype": "<PERSON><PERSON>", "label": "Generate Service Invoice"}, {"fieldname": "column_break_hqjie", "fieldtype": "Column Break"}, {"fieldname": "tra_clearance_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "TRA Clearance Total"}, {"fieldname": "port_clearance_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Port Clearance Total"}, {"fieldname": "shipment_clearance_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Shipment Clearance Total"}, {"fieldname": "physical_clearance_total", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "label": "Physical Verification Total"}, {"fieldname": "section_break_0qrth", "fieldtype": "Section Break"}, {"fieldname": "column_break_wv5mn", "fieldtype": "Column Break"}, {"fieldname": "column_break_fp5jw", "fieldtype": "Column Break"}, {"fieldname": "total_charges_sum", "fieldtype": "<PERSON><PERSON><PERSON><PERSON>", "in_list_view": 1, "in_standard_filter": 1, "label": "Total Charges"}], "index_web_pages_for_search": 1, "links": [], "modified": "2024-10-18 09:52:01.724931", "modified_by": "Administrator", "module": "Clearing", "name": "Clearing Charges", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}