{"autoname": "format:{YY}-{fieldname}-{#####}", "creation": "2020-02-14 00:21:21.344774", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["enable", "label", "fieldname", "fieldtype", "days_before_or_after", "days", "is_child_field", "doctype_name"], "fields": [{"fieldname": "label", "fieldtype": "Data", "in_list_view": 1, "label": "Label", "read_only": 1}, {"fieldname": "fieldname", "fieldtype": "Data", "in_list_view": 1, "label": "Field Name", "read_only": 1}, {"fieldname": "fieldtype", "fieldtype": "Data", "in_list_view": 1, "label": "Field Type", "read_only": 1}, {"default": "Days Before", "fieldname": "days_before_or_after", "fieldtype": "Select", "in_list_view": 1, "label": "Days Before or After", "options": "Days Before\nDays After"}, {"columns": 1, "default": "0", "fieldname": "days", "fieldtype": "Select", "in_list_view": 1, "label": "Days", "options": "0\n1\n2\n3\n4\n5\n6\n7\n8\n9\n10"}, {"default": "1", "fieldname": "enable", "fieldtype": "Check", "in_list_view": 1, "label": "Enable "}, {"default": "0", "fieldname": "is_child_field", "fieldtype": "Check", "label": "Is <PERSON>", "read_only": 1}, {"fieldname": "doctype_name", "fieldtype": "Data", "label": "DocType Name", "read_only": 1}], "istable": 1, "modified": "2020-02-19 04:56:56.120969", "modified_by": "Administrator", "module": "Extra Notifications", "name": "Date Fields", "owner": "Administrator", "permissions": [], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "track_changes": 1}