{"allow_rename": 1, "autoname": "Prompt", "creation": "2020-02-07 21:33:09.995629", "doctype": "DocType", "document_type": "System", "engine": "InnoDB", "field_order": ["enabled", "column_break_2", "channel", "telegram_user", "sms_recipients_section", "recipients", "dynamic_recipients", "filters", "subject", "document_type", "is_standard", "module", "col_break_1", "event", "method", "date_changed", "days_in_advance", "value_changed", "section_break_9", "condition", "column_break_6", "html_7", "property_section", "set_property_after_alert", "property_value", "column_break_5", "message_sb", "message", "message_examples", "slack_message_examples", "view_properties", "column_break_25", "attach_print", "print_format"], "fields": [{"default": "1", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}, {"fieldname": "column_break_2", "fieldtype": "Column Break"}, {"default": "SMS", "fieldname": "channel", "fieldtype": "Select", "label": "Channel", "options": "SMS", "reqd": 1, "set_only_once": 1}, {"depends_on": "eval:doc.channel=='Telegram'", "fieldname": "telegram_user", "fieldtype": "Link", "label": "Telegram Channel", "options": "Telegram User Settings"}, {"fieldname": "filters", "fieldtype": "Section Break", "label": "Filters"}, {"description": "To add dynamic subject, use jinja tags like\n\n<div><pre><code>{{ doc.name }} Delivered</code></pre></div>", "fieldname": "subject", "fieldtype": "Data", "ignore_xss_filter": 1, "in_list_view": 1, "label": "Subject", "reqd": 1}, {"fieldname": "document_type", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Document Type", "options": "DocType", "reqd": 1, "search_index": 1}, {"default": "0", "fieldname": "is_standard", "fieldtype": "Check", "label": "Is Standard"}, {"depends_on": "is_standard", "fieldname": "module", "fieldtype": "Link", "in_standard_filter": 1, "label": "<PERSON><PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>"}, {"fieldname": "col_break_1", "fieldtype": "Column Break"}, {"fieldname": "event", "fieldtype": "Select", "in_list_view": 1, "label": "Send <PERSON><PERSON>", "options": "\nNew\nSave\nSubmit\nCancel\nDays After\nDays Before\nValue Change\nMethod\nCustom", "reqd": 1, "search_index": 1}, {"depends_on": "eval:doc.event=='Method'", "description": "Trigger on valid methods like \"before_insert\", \"after_update\", etc (will depend on the DocType selected)", "fieldname": "method", "fieldtype": "Data", "label": "Trigger Method"}, {"depends_on": "eval:doc.event==\"Days After\" || doc.event==\"Days Before\"", "description": "Send alert if date matches this field's value", "fieldname": "date_changed", "fieldtype": "Select", "label": "Reference Date"}, {"default": "0", "depends_on": "eval:doc.event==\"Days After\" || doc.event==\"Days Before\"", "description": "Send days before or after the reference date", "fieldname": "days_in_advance", "fieldtype": "Int", "label": "Days Before or After"}, {"depends_on": "eval:doc.event==\"Value Change\"", "description": "Send alert if this field's value changes", "fieldname": "value_changed", "fieldtype": "Select", "label": "Value Changed"}, {"fieldname": "section_break_9", "fieldtype": "Section Break"}, {"description": "Optional: The alert will be sent if this expression is true", "fieldname": "condition", "fieldtype": "Code", "ignore_xss_filter": 1, "in_list_view": 1, "label": "Condition"}, {"fieldname": "column_break_6", "fieldtype": "Column Break"}, {"fieldname": "html_7", "fieldtype": "HTML", "options": "<p><strong>Condition Examples:</strong></p>\n<pre>doc.status==\"Open\"<br>doc.due_date==nowdate()<br>doc.total &gt; 40000\n</pre>\n"}, {"collapsible": 1, "fieldname": "property_section", "fieldtype": "Section Break", "label": "Set Property After Alert"}, {"fieldname": "set_property_after_alert", "fieldtype": "Select", "label": "Set Property After Alert"}, {"fieldname": "property_value", "fieldtype": "Data", "label": "Value To Be Set"}, {"depends_on": "eval:doc.channel=='Email'", "fieldname": "column_break_5", "fieldtype": "Section Break", "label": "Recipients"}, {"fieldname": "message_sb", "fieldtype": "Section Break", "label": "Message"}, {"default": "Add your message here", "fieldname": "message", "fieldtype": "Code", "ignore_xss_filter": 1, "label": "Message"}, {"depends_on": "eval:doc.channel=='Email'", "fieldname": "message_examples", "fieldtype": "HTML", "label": "Message Examples", "options": "<h5>Message Example</h5>\n\n<pre>&lt;h3&gt;Order Overdue&lt;/h3&gt;\n\n&lt;p&gt;Transaction {{ doc.name }} has exceeded Due Date. Please take necessary action.&lt;/p&gt;\n\n&lt;!-- show last comment --&gt;\n{% if comments %}\nLast comment: {{ comments[-1].comment }} by {{ comments[-1].by }}\n{% endif %}\n\n&lt;h4&gt;Details&lt;/h4&gt;\n\n&lt;ul&gt;\n&lt;li&gt;Customer: {{ doc.customer }}\n&lt;li&gt;Amount: {{ doc.grand_total }}\n&lt;/ul&gt;\n</pre>"}, {"depends_on": "eval:doc.channel=='Slack'", "fieldname": "slack_message_examples", "fieldtype": "HTML", "label": "Message Examples", "options": "<h5>Message Example</h5>\n\n<pre>*Order Overdue*\n\nTransaction {{ doc.name }} has exceeded Due Date. Please take necessary action.\n\n<!-- show last comment -->\n{% if comments %}\nLast comment: {{ comments[-1].comment }} by {{ comments[-1].by }}\n{% endif %}\n\n*Details*\n\n• Customer: {{ doc.customer }}\n• Amount: {{ doc.grand_total }}\n</pre>"}, {"fieldname": "view_properties", "fieldtype": "<PERSON><PERSON>", "label": "View Properties (via Customize Form)"}, {"collapsible": 1, "collapsible_depends_on": "attach_print", "fieldname": "column_break_25", "fieldtype": "Section Break", "label": "Print Settings"}, {"default": "0", "fieldname": "attach_print", "fieldtype": "Check", "label": "Attach Print"}, {"depends_on": "attach_print", "fieldname": "print_format", "fieldtype": "Link", "hidden": 1, "label": "Print Format", "options": "Print Format"}, {"fieldname": "recipients", "fieldtype": "Table", "label": "Recipients", "options": "SMS Contact List"}, {"fieldname": "sms_recipients_section", "fieldtype": "Section Break", "label": "SMS Recipients"}, {"default": "0", "fieldname": "dynamic_recipients", "fieldtype": "Check", "label": "Dynamic Recipients"}], "icon": "fa fa-envelope", "modified": "2020-05-08 23:06:37.254428", "modified_by": "Administrator", "module": "Erpnext Telegram Integration", "name": "SMS Notification", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "export": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "title_field": "subject", "track_changes": 1}