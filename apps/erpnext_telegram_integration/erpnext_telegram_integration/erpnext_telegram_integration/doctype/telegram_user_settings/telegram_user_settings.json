{"actions": [], "allow_rename": 1, "autoname": "format:{telegram_user}-{telegram_settings}", "creation": "2019-12-01 02:31:10.562643", "doctype": "DocType", "editable_grid": 1, "engine": "InnoDB", "field_order": ["party", "telegram_user", "telegram_user_name", "telegram_settings", "section_break_4", "is_group_chat", "generate_telegram_token", "telegram_token", "column_break_9", "get_chat_id", "telegram_chat_id"], "fields": [{"fieldname": "party", "fieldtype": "Link", "in_list_view": 1, "label": "Party", "options": "DocType", "reqd": 1}, {"fieldname": "telegram_user", "fieldtype": "Dynamic Link", "in_list_view": 1, "label": "Telegram User", "options": "party", "reqd": 1}, {"fieldname": "telegram_user_name", "fieldtype": "Data", "label": "Telegram User Name", "read_only": 1}, {"fieldname": "section_break_4", "fieldtype": "Section Break"}, {"fieldname": "telegram_token", "fieldtype": "Data", "label": "Telegram Token"}, {"fieldname": "get_chat_id", "fieldtype": "<PERSON><PERSON>", "label": "Get Chat ID"}, {"fieldname": "telegram_chat_id", "fieldtype": "Data", "label": "Telegram Chat ID", "read_only": 1}, {"fieldname": "telegram_settings", "fieldtype": "Link", "in_list_view": 1, "label": "Telegram Settings", "options": "Telegram Settings", "reqd": 1}, {"default": "0", "fieldname": "is_group_chat", "fieldtype": "Check", "label": "Is Group Chat"}, {"fieldname": "column_break_9", "fieldtype": "Column Break"}, {"fieldname": "generate_telegram_token", "fieldtype": "<PERSON><PERSON>", "label": "Generate Telegram Token"}], "links": [], "modified": "2023-01-16 14:21:40.062556", "modified_by": "Administrator", "module": "Erpnext Telegram Integration", "name": "Telegram User Settings", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "if_owner": 1, "read": 1, "role": "All", "write": 1}], "quick_entry": 1, "sort_field": "modified", "sort_order": "DESC", "states": [], "track_changes": 1}