name: CI

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - develop

jobs:
  linters:
    runs-on: ubuntu-latest
    steps:

      - name: Checkout Code Repository
        uses: actions/checkout@v2

      - name: Set up Python 3.8
        uses: actions/setup-python@v2
        with:
          python-version: 3.8

      - name: Install and Run Pre-commit
        uses: pre-commit/action@v2.0.0

  tests:
    runs-on: ubuntu-18.04
    timeout-minutes: 20

    strategy:
      fail-fast: false

    name: Server

    services:
      mysql:
        image: mariadb:10.3
        env:
          MYSQL_ALLOW_EMPTY_PASSWORD: YES
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=5s --health-timeout=2s --health-retries=3

    steps:
      - name: <PERSON><PERSON>
        uses: actions/checkout@v2

      - name: Setup Python
        uses: actions/setup-python@v2
        with:
          python-version: 3.8

      - name: Setup Node
        uses: actions/setup-node@v2
        with:
          node-version: 14
          check-latest: true

      - name: Add to Hosts
        run: echo "127.0.0.1 test_site" | sudo tee -a /etc/hosts

      - name: <PERSON><PERSON> pip
        uses: actions/cache@v2
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('**/*requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-
            ${{ runner.os }}-
      - name: Cache node modules
        uses: actions/cache@v2
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-

      - name: Get yarn cache directory path
        id: yarn-cache-dir-path
        run: echo "::set-output name=dir::$(yarn cache dir)"

      - uses: actions/cache@v2
        id: yarn-cache
        with:
          path: ${{ steps.yarn-cache-dir-path.outputs.dir }}
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: |
            ${{ runner.os }}-yarn-

      - name: Install
        run: |
          bash ${GITHUB_WORKSPACE}/.github/helper/install.sh
          cd ~/frappe-bench/ && source env/bin/activate
          cd apps/non_profit && pip install -r dev-requirements.txt
        env:
          TYPE: server

      - name: Run Tests
        run: cd ~/frappe-bench/ && bench --site test_site run-tests --app non_profit
        env:
          TYPE: server
