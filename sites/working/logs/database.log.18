2025-04-01 16:16:17,728 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-01 16:16:17,965 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-01 16:16:18,001 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-01 16:16:18,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-01 16:16:18,563 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-01 16:16:18,592 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-01 16:16:18,781 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-04-01 16:16:18,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-04-01 16:16:19,309 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0, MODIFY `volume` decimal(21,9) not null default 0
2025-04-01 21:11:37,231 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parent varchar(140)
2025-04-01 21:11:37,232 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parenttype varchar(140)
2025-04-01 21:11:37,233 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parentfield varchar(140)
2025-04-01 21:11:37,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` ADD COLUMN `quantiti_of_hs_code` int(11) not null default 0
2025-04-01 21:11:37,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `volume` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-04-02 15:41:47,304 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-02 15:41:48,128 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-02 15:41:49,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-02 15:41:51,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-02 15:41:52,076 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-02 15:41:52,611 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-02 15:41:52,902 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-02 15:41:52,930 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-02 15:41:53,204 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-02 15:41:53,808 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-02 15:41:53,836 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-02 15:41:54,677 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `volume` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-04-02 15:41:54,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabMulti-Dimensional Budget` ADD COLUMN `spent_amount` varchar(140), ADD COLUMN `planned_amount` decimal(21,9) not null default 0, ADD COLUMN `allowed_amount` decimal(21,9) not null default 0, ADD COLUMN `unspent_amount` decimal(21,9) not null default 0
2025-04-02 15:41:54,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabMulti-Dimensional Budget` MODIFY `percentage_used` decimal(21,9) not null default 0
2025-04-02 15:41:55,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabMulti-Dimensional Budget Detail` ADD COLUMN `accounting_dimension_name` varchar(140)
2025-04-03 11:06:09,031 WARNING database DDL Query made to DB:
ALTER TABLE `tabCF Delivery Note` ADD COLUMN `staff_id` varchar(140)
2025-04-03 11:22:27,085 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-03 11:22:27,885 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-03 11:22:29,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-03 11:22:31,091 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-03 11:22:31,329 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-03 11:22:31,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-03 11:22:32,093 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-03 11:22:32,123 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-03 11:22:32,382 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-03 11:22:32,957 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-03 11:22:33,010 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-03 11:22:33,695 WARNING database DDL Query made to DB:
ALTER TABLE `tabTarget Revenue` ADD COLUMN `amended_from` varchar(140)
2025-04-03 11:22:33,721 WARNING database DDL Query made to DB:
ALTER TABLE `tabTarget Revenue` MODIFY `target_amount` decimal(21,9) not null default 0, MODIFY `target_percentage` decimal(21,9) not null default 0, MODIFY `collected_amount` decimal(21,9) not null default 0
2025-04-03 11:22:33,765 WARNING database DDL Query made to DB:
ALTER TABLE `tabTarget Revenue` ADD INDEX `amended_from_index`(`amended_from`)
2025-04-03 11:40:51,825 WARNING database DDL Query made to DB:
ALTER TABLE `tabCF Delivery Note` ADD COLUMN `staff_name` varchar(140)
2025-04-03 17:55:29,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` ADD COLUMN `container_deposit_returned` int(1) not null default 0
2025-04-03 17:55:29,897 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-04-03 17:58:32,615 WARNING database DDL Query made to DB:
create table `tabContainer Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-03 17:59:21,811 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Location` ADD COLUMN `location_name` varchar(140), ADD COLUMN `address` varchar(140)
2025-04-03 18:02:49,187 WARNING database DDL Query made to DB:
ALTER TABLE `tabCF Delivery Note` ADD COLUMN `loading_date` date
2025-04-06 14:29:24,479 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-06 14:29:25,296 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-06 14:29:26,390 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-06 14:29:28,375 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-06 14:29:28,634 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-06 14:29:29,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-06 14:29:29,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-06 14:29:29,476 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-06 14:29:29,752 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-06 14:29:30,387 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-06 14:29:30,411 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-06 14:29:30,689 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `total_charges` decimal(21,9) not null default 0, MODIFY `container_deposit_amount` decimal(21,9) not null default 0
2025-04-06 14:29:31,520 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany Budget Control` ADD COLUMN `action_if_budget_exceeded_on_mr` varchar(140) default 'Stop', ADD COLUMN `action_if_budget_exceeded_on_po` varchar(140) default 'Stop', ADD COLUMN `action_if_budget_exceeded_on_actual` varchar(140) default 'Stop', ADD COLUMN `action_if_no_target_revenue` varchar(140) default 'Warn', ADD COLUMN `group_income_account` varchar(140)
2025-04-06 14:29:31,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany Budget Control` MODIFY `fixed_target_revenue_amount` decimal(21,9) not null default 0, ADD UNIQUE INDEX IF NOT EXISTS company (`company`)
2025-04-06 14:29:31,603 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany Budget Control` ADD INDEX `applicable_on_material_request_index`(`applicable_on_material_request`), ADD INDEX `action_if_budget_exceeded_on_mr_index`(`action_if_budget_exceeded_on_mr`), ADD INDEX `applicable_on_purchase_order_index`(`applicable_on_purchase_order`)
2025-04-06 14:29:31,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabMulti-Dimensional Budget` ADD COLUMN `start_date` date, ADD COLUMN `end_date` date, ADD COLUMN `amount_used` decimal(21,9) not null default 0, ADD COLUMN `unused_amount` decimal(21,9) not null default 0, ADD COLUMN `action_if_budget_exceeded_on_mr` varchar(140) default 'Stop', ADD COLUMN `action_if_budget_exceeded_on_po` varchar(140) default 'Stop', ADD COLUMN `action_if_budget_exceeded` varchar(140) default 'Stop'
2025-04-06 14:29:31,800 WARNING database DDL Query made to DB:
ALTER TABLE `tabMulti-Dimensional Budget` MODIFY `percentage_used` decimal(21,9) not null default 0, MODIFY `allowed_amount` decimal(21,9) not null default 0, MODIFY `planned_amount` decimal(21,9) not null default 0
2025-04-06 14:29:31,932 WARNING database DDL Query made to DB:
ALTER TABLE `tabMulti-Dimensional Budget` ADD INDEX `company_index`(`company`), ADD INDEX `start_date_index`(`start_date`), ADD INDEX `end_date_index`(`end_date`), ADD INDEX `allowed_amount_index`(`allowed_amount`), ADD INDEX `amount_used_index`(`amount_used`), ADD INDEX `unused_amount_index`(`unused_amount`), ADD INDEX `percentage_used_index`(`percentage_used`), ADD INDEX `applicable_on_material_request_index`(`applicable_on_material_request`), ADD INDEX `action_if_budget_exceeded_on_mr_index`(`action_if_budget_exceeded_on_mr`), ADD INDEX `applicable_on_purchase_order_index`(`applicable_on_purchase_order`), ADD INDEX `action_if_budget_exceeded_on_po_index`(`action_if_budget_exceeded_on_po`), ADD INDEX `applicable_on_booking_actual_expenses_index`(`applicable_on_booking_actual_expenses`), ADD INDEX `action_if_budget_exceeded_index`(`action_if_budget_exceeded`)
2025-04-06 22:58:21,389 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-06 22:58:22,234 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-06 22:58:23,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-06 22:58:25,590 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-06 22:58:25,787 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-06 22:58:26,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-06 22:58:26,665 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-06 22:58:26,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-06 22:58:26,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-06 22:58:27,565 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-06 22:58:27,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-07 23:27:15,093 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-07 23:27:15,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-07 23:27:17,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-07 23:27:19,136 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-07 23:27:19,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-07 23:27:20,013 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-07 23:27:20,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-07 23:27:20,337 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-07 23:27:20,604 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-07 23:27:21,249 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-07 23:27:21,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-08 15:54:55,283 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-08 15:54:56,112 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-08 15:54:57,333 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-08 15:54:59,301 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-08 15:54:59,574 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-08 15:55:00,151 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-08 15:55:00,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-08 15:55:00,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-08 15:55:00,691 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-08 15:55:01,334 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-08 15:55:01,361 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-08 16:24:41,563 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-08 16:24:42,248 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-08 16:24:43,219 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-08 16:24:43,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD COLUMN `recreate_stock_ledgers` int(1) not null default 0
2025-04-08 16:24:43,594 WARNING database DDL Query made to DB:
ALTER TABLE `tabRepost Item Valuation` ADD INDEX `creation`(`creation`)
2025-04-08 16:24:45,353 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-08 16:24:45,534 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-08 16:24:46,041 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-08 16:24:46,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-08 16:24:46,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-08 16:24:46,594 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-08 16:24:47,121 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-08 16:24:47,153 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-08 16:25:53,956 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-08 16:25:54,687 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-08 16:25:55,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-08 16:25:57,491 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-08 16:25:57,662 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-08 16:25:58,168 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-08 16:25:58,453 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-08 16:25:58,479 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-08 16:25:58,663 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-08 16:25:59,216 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-08 16:25:59,249 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-09 10:29:56,163 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-09 10:29:57,077 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-09 10:29:58,368 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-09 10:30:00,789 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-09 10:30:01,019 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-09 10:30:01,603 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-09 10:30:01,914 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-09 10:30:01,942 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-09 10:30:02,202 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-09 10:30:02,864 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-09 10:30:02,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-09 10:30:03,369 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany Budget Control` MODIFY `fixed_target_revenue_amount` decimal(21,9) not null default 0
2025-04-09 10:41:52,827 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-09 10:41:54,075 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-09 10:41:55,700 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-09 10:41:59,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-09 10:41:59,278 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-09 10:42:00,010 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-09 10:42:00,365 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-09 10:42:00,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-09 10:42:00,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-09 10:42:01,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-09 10:42:01,552 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-09 13:48:32,459 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-09 13:48:33,458 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-09 13:48:34,452 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-09 13:48:35,766 WARNING database DDL Query made to DB:
ALTER TABLE `tabInter Company Stock Transfer` ADD COLUMN `inter_company_material_request` varchar(140)
2025-04-09 13:48:35,929 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile Attachment` MODIFY `file_attachment` text
2025-04-09 13:48:36,130 WARNING database DDL Query made to DB:
create table `tabInter Company Material Request Details` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_name` varchar(140),
`item_code` varchar(140),
`uom` varchar(140),
`qty` decimal(21,9) not null default 0,
`batch_no` varchar(140),
`bom_no` varchar(140),
`transfer_qty` decimal(21,9) not null default 0,
`s_warehouse` varchar(140),
`t_warehouse` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-09 13:48:36,277 WARNING database DDL Query made to DB:
create table `tabInter Company Material Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`from_company` varchar(140),
`default_from_warehouse` varchar(140),
`to_company` varchar(140),
`default_to_warehouse` varchar(140),
`inter_company_stock_transfer` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-09 13:48:37,168 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-09 13:48:37,399 WARNING database DDL Query made to DB:
ALTER TABLE `tabTransport Assignment` MODIFY `amount` decimal(21,9) not null default 0
2025-04-09 13:48:37,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-09 13:48:38,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-09 13:48:38,357 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-09 13:48:38,384 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-09 13:48:38,601 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-09 13:48:39,232 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-09 13:48:39,263 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-09 13:48:50,752 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-04-09 16:11:29,919 WARNING database DDL Query made to DB:
ALTER TABLE `tabContainer Location` ADD UNIQUE INDEX IF NOT EXISTS location_name (`location_name`)
2025-04-09 16:14:35,478 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parent varchar(140)
2025-04-09 16:14:35,479 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parenttype varchar(140)
2025-04-09 16:14:35,479 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parentfield varchar(140)
2025-04-09 16:14:35,565 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `volume` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-04-10 13:22:21,110 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-10 13:22:21,972 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-10 13:22:22,863 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-10 13:22:24,508 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-10 13:22:24,752 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-10 13:22:25,298 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-10 13:22:25,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-10 13:22:25,638 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-10 13:22:25,913 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-10 13:22:26,726 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-10 13:22:26,757 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-10 13:22:27,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `value` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0, MODIFY `volume` decimal(21,9) not null default 0
2025-04-10 13:22:27,883 WARNING database DDL Query made to DB:
create table `tabMulti-Dimensional Budget Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account` varchar(140),
`budget_amount` decimal(21,9) not null default 0,
index `account`(`account`),
index `budget_amount`(`budget_amount`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-10 13:22:28,083 WARNING database DDL Query made to DB:
ALTER TABLE `tabTarget Revenue` ADD COLUMN `earned_amount` decimal(21,9) not null default 0
2025-04-10 13:22:28,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabTarget Revenue` MODIFY `target_percentage` decimal(21,9) not null default 0, MODIFY `target_amount` decimal(21,9) not null default 0
2025-04-10 13:22:38,640 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0
2025-04-12 16:19:38,009 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-12 16:19:38,855 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-12 16:19:39,869 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-12 16:19:41,619 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-12 16:19:41,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-12 16:19:42,324 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-12 16:19:42,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-12 16:19:42,614 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-12 16:19:42,843 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-12 16:19:43,466 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-12 16:19:43,491 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-12 16:19:43,778 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-04-12 16:19:43,913 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-04-12 16:19:44,042 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-04-12 16:19:44,689 WARNING database DDL Query made to DB:
ALTER TABLE `tabPort Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-04-12 16:19:44,945 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0, MODIFY `volume` decimal(21,9) not null default 0
2025-04-12 16:19:57,532 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-04-12 21:54:45,842 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-12 21:54:46,573 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-12 21:54:47,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-12 21:54:49,320 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-12 21:54:49,583 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-12 21:54:50,124 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-12 21:54:50,376 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-12 21:54:50,409 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-12 21:54:50,636 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-12 21:54:51,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-12 21:54:51,236 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-12 21:55:00,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0
2025-04-12 21:55:35,704 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-12 21:55:36,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-12 21:55:37,313 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-12 21:55:39,192 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-12 21:55:39,418 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-12 21:55:40,006 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-12 21:55:40,268 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-12 21:55:40,295 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-12 21:55:40,482 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-12 21:55:41,057 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-12 21:55:41,090 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-12 21:55:51,024 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0
2025-04-13 14:25:14,618 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-13 14:25:15,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-13 14:25:16,537 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-13 14:25:18,446 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-13 14:25:18,713 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-13 14:25:19,244 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-13 14:25:19,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-13 14:25:19,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-13 14:25:19,892 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-13 14:25:20,550 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-13 14:25:20,582 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-13 14:25:20,926 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line Clearance` MODIFY `container_deposit_amount` decimal(21,9) not null default 0, MODIFY `total_charges` decimal(21,9) not null default 0
2025-04-13 14:25:21,486 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `weight` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0, MODIFY `volume` decimal(21,9) not null default 0
2025-04-13 14:25:32,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0
2025-04-14 12:49:05,272 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-14 12:49:06,018 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Document State` ADD COLUMN `send_email` int(1) not null default 1
2025-04-14 12:49:06,716 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-14 12:49:07,734 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-14 12:49:10,277 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-14 12:49:10,523 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-14 12:49:11,253 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-14 12:49:11,646 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-14 12:49:11,673 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-14 12:49:11,953 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-14 12:49:12,655 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-14 12:49:12,682 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-14 12:49:24,229 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-04-15 12:09:56,650 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parent varchar(140)
2025-04-15 12:09:56,652 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parenttype varchar(140)
2025-04-15 12:09:56,653 WARNING database DDL Query made to DB:
alter table `tabCargo` add column if not exists parentfield varchar(140)
2025-04-15 12:09:56,746 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `value` decimal(21,9) not null default 0, MODIFY `volume` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-04-15 16:53:03,594 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-04-16 08:58:44,095 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-16 08:58:44,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-16 08:58:46,023 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-16 08:58:48,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-16 08:58:48,351 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-16 08:58:48,929 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-16 08:58:49,204 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-16 08:58:49,233 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-16 08:58:49,453 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-16 08:58:50,062 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-16 08:58:50,092 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-16 08:58:50,426 WARNING database DDL Query made to DB:
ALTER TABLE `tabTRA Clearance` MODIFY `total_charges` decimal(21,9) not null default 0
2025-04-16 08:58:50,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabCargo` MODIFY `volume` decimal(21,9) not null default 0, MODIFY `value` decimal(21,9) not null default 0, MODIFY `weight` decimal(21,9) not null default 0
2025-04-16 08:59:01,440 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0
2025-04-16 09:16:38,557 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-16 09:16:39,055 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `protect_attached_files` int(1) not null default 0
2025-04-16 09:16:39,874 WARNING database DDL Query made to DB:
ALTER TABLE `tabEvent` MODIFY `google_meet_link` text
2025-04-16 09:16:40,143 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-16 09:16:40,505 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `bank_charges` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0
2025-04-16 09:16:41,094 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `allow_partial_payment` int(1) not null default 0, ADD COLUMN `project` varchar(140)
2025-04-16 09:16:41,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD INDEX `creation`(`creation`)
2025-04-16 09:16:41,747 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-16 09:16:42,684 WARNING database DDL Query made to DB:
ALTER TABLE `tabAsset` MODIFY `purchase_amount` decimal(21,9) not null default 0, MODIFY `value_after_depreciation` decimal(21,9) not null default 0, MODIFY `gross_purchase_amount` decimal(21,9) not null default 0, MODIFY `total_asset_cost` decimal(21,9) not null default 0, MODIFY `additional_asset_cost` decimal(21,9) not null default 0, MODIFY `opening_accumulated_depreciation` decimal(21,9) not null default 0
2025-04-16 09:16:44,007 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-16 09:16:44,190 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-16 09:16:44,736 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-16 09:16:44,964 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD COLUMN `half_day_status` varchar(140)
2025-04-16 09:16:44,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-16 09:16:45,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-16 09:16:45,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-16 09:16:45,959 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-16 09:16:45,994 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-16 09:16:55,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0
2025-04-16 12:53:26,806 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-16 12:53:27,516 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-16 12:53:28,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-16 12:53:30,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-16 12:53:30,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-16 12:53:30,830 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-16 12:53:31,068 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-16 12:53:31,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-16 12:53:31,357 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-16 12:53:31,928 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-16 12:53:31,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-16 12:53:42,314 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0
2025-04-18 14:20:08,784 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-18 14:20:09,489 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-18 14:20:10,362 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-18 14:20:12,181 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-18 14:20:12,427 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-18 14:20:12,935 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-18 14:20:13,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-18 14:20:13,207 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-18 14:20:13,393 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-18 14:20:13,952 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-18 14:20:13,978 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-18 14:20:24,830 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0
2025-04-19 18:57:33,617 WARNING database DDL Query made to DB:
ALTER TABLE `tabMulti-Dimensional Budget Account` MODIFY `budget_amount` decimal(21,9) not null default 0
2025-04-19 18:57:33,837 WARNING database DDL Query made to DB:
ALTER TABLE `tabTarget Revenue` MODIFY `earned_amount` decimal(21,9) not null default 0, MODIFY `target_amount` decimal(21,9) not null default 0, MODIFY `target_percentage` decimal(21,9) not null default 0
2025-04-19 18:57:34,016 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany Budget Control` MODIFY `fixed_target_revenue_amount` decimal(21,9) not null default 0
2025-04-19 18:57:55,663 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-19 18:57:56,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-19 18:57:57,356 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-19 18:57:59,100 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-19 18:57:59,287 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-19 18:57:59,758 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-19 18:58:00,036 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-19 18:58:00,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-19 18:58:00,321 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-19 18:58:00,898 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-19 18:58:00,931 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-19 18:58:10,977 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0
2025-04-19 21:25:36,155 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-19 21:25:36,880 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-19 21:25:37,812 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-19 21:25:39,554 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-19 21:25:39,766 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-19 21:25:40,257 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-19 21:25:40,541 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-19 21:25:40,571 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-19 21:25:40,816 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-19 21:25:41,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-19 21:25:41,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-19 21:25:51,906 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0
2025-04-19 21:46:12,425 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-19 21:46:13,225 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-19 21:46:14,107 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-19 21:46:15,824 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-19 21:46:16,054 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-19 21:46:16,527 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-19 21:46:16,759 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-19 21:46:16,786 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-19 21:46:16,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-19 21:46:17,556 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-19 21:46:17,588 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-19 21:46:27,546 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0
2025-04-19 21:53:29,100 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-19 21:53:29,801 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-19 21:53:30,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-19 21:53:32,557 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-19 21:53:32,780 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-19 21:53:33,267 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-19 21:53:33,512 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-19 21:53:33,542 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-19 21:53:33,840 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-19 21:53:34,401 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-19 21:53:34,434 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-19 21:53:44,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0
2025-04-21 12:44:39,178 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-21 12:44:40,068 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-21 12:44:41,350 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-21 12:44:43,351 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-21 12:44:43,631 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-21 12:44:44,236 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-21 12:44:44,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-21 12:44:44,597 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-21 12:44:44,870 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-21 12:44:45,549 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-21 12:44:45,581 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-21 12:44:57,008 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0
2025-04-24 11:19:41,444 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-24 11:19:42,123 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-24 11:19:43,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-24 11:19:44,924 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-24 11:19:45,177 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-24 11:19:45,750 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-24 11:19:46,034 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-24 11:19:46,064 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-24 11:19:46,302 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-24 11:19:46,836 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-24 11:19:46,861 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-24 11:19:57,385 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `custom_total_trade_in_value` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `custom_trade_in_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `custom_trade_in_incoming_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-04-24 17:02:58,316 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-24 17:02:59,098 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-24 17:02:59,569 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `distributed_discount_amount` decimal(21,9) not null default 0
2025-04-24 17:33:35,127 WARNING database DDL Query made to DB:
ALTER TABLE `tabClearing File` ADD COLUMN `awbbl_no` varchar(140)
