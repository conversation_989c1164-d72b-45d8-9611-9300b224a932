2025-03-31 14:25:38,751 WARNING database DDL Query made to DB:
create table `tabCourse Topic` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`topic` varchar(140),
`topic_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:38,891 WARNING database DDL Query made to DB:
create table `tabQuiz Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enrollment` varchar(140),
`student` varchar(140),
`course` varchar(140),
`quiz` varchar(140),
`status` varchar(140),
`activity_date` varchar(140),
`score` varchar(140),
`time_taken` decimal(21,9),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:39,026 WARNING database DDL Query made to DB:
create table `tabStudent Sibling` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`studying_in_same_institute` varchar(140),
`full_name` varchar(140),
`gender` varchar(140),
`student` varchar(140),
`institution` varchar(140),
`program` varchar(140),
`date_of_birth` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:39,135 WARNING database DDL Query made to DB:
create table `tabGrading Scale Interval` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`grade_code` varchar(140),
`threshold` decimal(21,9) not null default 0,
`grade_description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:39,239 WARNING database DDL Query made to DB:
create table `tabQuiz Question` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question_link` varchar(140),
`question` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:39,346 WARNING database DDL Query made to DB:
create table `tabStudent Language` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`language_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:39,447 WARNING database DDL Query made to DB:
create table `tabStudent Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:39,553 WARNING database DDL Query made to DB:
create table `tabProgram Course` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`course_name` varchar(140),
`required` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:39,769 WARNING database DDL Query made to DB:
create table `tabStudent Group Instructor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`instructor` varchar(140),
`instructor_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:39,906 WARNING database DDL Query made to DB:
create table `tabStudent Leave Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student` varchar(140),
`student_name` varchar(140),
`from_date` date,
`to_date` date,
`total_leave_days` decimal(21,9) not null default 0,
`attendance_based_on` varchar(140) default 'Student Group',
`student_group` varchar(140),
`course_schedule` varchar(140),
`mark_as_present` int(1) not null default 0,
`reason` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:40,042 WARNING database DDL Query made to DB:
create table `tabInstructor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`instructor_name` varchar(140),
`employee` varchar(140),
`gender` varchar(140),
`status` varchar(140) default 'Active',
`naming_series` varchar(140),
`department` varchar(140),
`image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:40,157 WARNING database DDL Query made to DB:
create table `tabTopic Content` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`content_type` varchar(140),
`content` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:40,444 WARNING database DDL Query made to DB:
create table `tabFee Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fee_structure` varchar(140),
`posting_date` date,
`due_date` date,
`naming_series` varchar(140),
`fee_creation_status` varchar(140),
`send_email` int(1) not null default 0,
`student_category` varchar(140),
`program` varchar(140),
`academic_year` varchar(140),
`academic_term` varchar(140),
`currency` varchar(140),
`total_amount` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`grand_total_in_words` varchar(240),
`letter_head` varchar(140),
`select_print_heading` varchar(140),
`receivable_account` varchar(140),
`income_account` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`cost_center` varchar(140),
`error_log` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:40,581 WARNING database DDL Query made to DB:
create table `tabAssessment Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_criteria` varchar(140),
`assessment_criteria_group` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:40,698 WARNING database DDL Query made to DB:
create table `tabStudent Admission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140) unique,
`published` int(1) not null default 0,
`enable_admission_application` int(1) not null default 0,
`academic_year` varchar(140),
`admission_start_date` date,
`admission_end_date` date,
`introduction` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:40,804 WARNING database DDL Query made to DB:
create table `tabStudent Guardian` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`guardian` varchar(140),
`guardian_name` varchar(140),
`relation` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:40,964 WARNING database DDL Query made to DB:
create table `tabStudent Applicant` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`naming_series` varchar(140),
`application_date` date,
`application_status` varchar(140),
`program` varchar(140),
`student_email_id` varchar(140) unique,
`student_admission` varchar(140),
`student_category` varchar(140),
`image` text,
`academic_year` varchar(140),
`academic_term` varchar(140),
`paid` int(1) not null default 0,
`title` varchar(140),
`amended_from` varchar(140),
`date_of_birth` date,
`gender` varchar(140),
`blood_group` varchar(140),
`student_mobile_number` varchar(140),
`nationality` varchar(140),
`address_line_1` varchar(140),
`address_line_2` varchar(140),
`city` varchar(140),
`state` varchar(140),
`pincode` varchar(140),
`country` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:41,072 WARNING database DDL Query made to DB:
create table `tabFee Schedule Student Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student_group` varchar(140),
`total_students` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:41,191 WARNING database DDL Query made to DB:
create table `tabTopic` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`topic_name` varchar(140) unique,
`description` text,
`hero_image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:41,309 WARNING database DDL Query made to DB:
create table `tabProgram Enrollment Fee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`academic_term` varchar(140),
`fee_structure` varchar(140),
`due_date` date,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:41,419 WARNING database DDL Query made to DB:
create table `tabCourse Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student_group` varchar(140),
`instructor` varchar(140),
`instructor_name` varchar(140),
`naming_series` varchar(140),
`program` varchar(140),
`course` varchar(140),
`color` varchar(140),
`schedule_date` date,
`room` varchar(140),
`from_time` time(6),
`to_time` time(6),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:41,561 WARNING database DDL Query made to DB:
create table `tabGuardian` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`guardian_name` varchar(140),
`email_address` varchar(140),
`mobile_number` varchar(140),
`alternate_number` varchar(140),
`date_of_birth` date,
`user` varchar(140),
`education` varchar(140),
`occupation` varchar(140),
`designation` varchar(140),
`work_address` text,
`image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:41,701 WARNING database DDL Query made to DB:
create table `tabAssessment Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_group_name` varchar(140) unique,
`parent_assessment_group` varchar(140),
`is_group` int(1) not null default 0,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:41,850 WARNING database DDL Query made to DB:
create table `tabStudent Group Student` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student` varchar(140),
`student_name` varchar(140),
`group_roll_number` int(11) not null default 0,
`active` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:42,120 WARNING database DDL Query made to DB:
create table `tabStudent Batch Name` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`batch_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:42,788 WARNING database DDL Query made to DB:
create table `tabFee Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category_name` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:43,399 WARNING database DDL Query made to DB:
create table `tabStudent Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student` varchar(140),
`student_name` varchar(140),
`type` varchar(140),
`date` date,
`academic_year` varchar(140),
`academic_term` varchar(140),
`program` varchar(140),
`student_batch` varchar(140),
`log` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:44,224 WARNING database DDL Query made to DB:
create table `tabQuestion` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` longtext,
`question_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:44,343 WARNING database DDL Query made to DB:
create table `tabGrading Scale` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`grading_scale_name` varchar(140) unique,
`amended_from` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:44,470 WARNING database DDL Query made to DB:
create table `tabQuiz` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`passing_score` decimal(21,9) not null default 75.0,
`max_attempts` int(11) not null default 1,
`grading_basis` varchar(140) default 'Latest Highest Score',
`is_time_bound` int(1) not null default 0,
`duration` decimal(21,9),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:44,599 WARNING database DDL Query made to DB:
create table `tabCourse Enrollment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`program_enrollment` varchar(140),
`program` varchar(140),
`enrollment_date` date,
`course` varchar(140),
`student` varchar(140),
`student_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:44,709 WARNING database DDL Query made to DB:
create table `tabQuiz Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` varchar(140),
`selected_option` varchar(140),
`quiz_result` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:44,820 WARNING database DDL Query made to DB:
create table `tabProgram` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`program_name` varchar(140) unique,
`department` varchar(140),
`hero_image` text,
`program_abbreviation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:44,951 WARNING database DDL Query made to DB:
create table `tabInstructor Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`academic_year` varchar(140),
`academic_term` varchar(140),
`department` varchar(140),
`program` varchar(140),
`course` varchar(140),
`student_group` varchar(140),
`other_details` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:45,060 WARNING database DDL Query made to DB:
create table `tabAssessment Result Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_criteria` varchar(140),
`maximum_score` decimal(21,9) not null default 0,
`score` decimal(21,9) not null default 0,
`grade` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:45,166 WARNING database DDL Query made to DB:
create table `tabStudent Group Creation Tool Course` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`group_based_on` varchar(140),
`course` varchar(140),
`batch` varchar(140),
`student_group_name` varchar(140),
`course_code` varchar(140),
`max_strength` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:45,280 WARNING database DDL Query made to DB:
create table `tabProgram Enrollment Tool Student` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student_applicant` varchar(140),
`student` varchar(140),
`student_name` varchar(140),
`student_batch_name` varchar(140),
`student_category` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:45,471 WARNING database DDL Query made to DB:
create table `tabGuardian Student` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student` varchar(140),
`student_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:45,565 WARNING database DDL Query made to DB:
create table `tabGuardian Interest` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interest` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:45,687 WARNING database DDL Query made to DB:
create table `tabProgram Enrollment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student` varchar(140),
`student_name` varchar(140),
`enrollment_date` date,
`program` varchar(140),
`academic_year` varchar(140),
`academic_term` varchar(140),
`image` text,
`student_category` varchar(140),
`student_batch_name` varchar(140),
`school_house` varchar(140),
`boarding_student` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:45,806 WARNING database DDL Query made to DB:
create table `tabArticle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`author` varchar(140),
`content` longtext,
`publish_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:45,937 WARNING database DDL Query made to DB:
create table `tabStudent Attendance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`student` varchar(140),
`student_name` varchar(140),
`student_mobile_number` varchar(140),
`course_schedule` varchar(140),
`student_group` varchar(140),
`date` date,
`status` varchar(140) default 'Present',
`leave_application` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `student`(`student`),
index `date`(`date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:46,070 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoom` ADD COLUMN `room_name` varchar(140), ADD COLUMN `room_number` varchar(140), ADD COLUMN `seating_capacity` varchar(140)
2025-03-31 14:25:46,163 WARNING database DDL Query made to DB:
create table `tabSchool House` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`house_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:46,271 WARNING database DDL Query made to DB:
create table `tabStudent Admission Program` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`program` varchar(140),
`min_age` int(11) not null default 0,
`max_age` int(11) not null default 0,
`description` text,
`application_fee` decimal(21,9) not null default 0,
`applicant_naming_series` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:46,491 WARNING database DDL Query made to DB:
create table `tabAssessment Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_plan` varchar(140),
`program` varchar(140),
`course` varchar(140),
`academic_year` varchar(140),
`academic_term` varchar(140),
`student` varchar(140),
`student_name` varchar(140),
`student_group` varchar(140),
`assessment_group` varchar(140),
`grading_scale` varchar(140),
`maximum_score` decimal(21,9) not null default 0,
`total_score` decimal(21,9) not null default 0,
`grade` varchar(140),
`comment` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:46,618 WARNING database DDL Query made to DB:
create table `tabStudent Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`academic_year` varchar(140),
`group_based_on` varchar(140),
`student_group_name` varchar(140) unique,
`max_strength` int(11) not null default 0,
`academic_term` varchar(140),
`program` varchar(140),
`batch` varchar(140),
`student_category` varchar(140),
`course` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:46,763 WARNING database DDL Query made to DB:
create table `tabCourse Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enrollment` varchar(140),
`course` varchar(140),
`student` varchar(140),
`content_type` varchar(140),
`content` varchar(140),
`activity_date` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:25:46,872 WARNING database DDL Query made to DB:
create table `tabStudent Siblings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140),
`gender` varchar(140),
`date_of_birth` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-03-31 14:26:24,880 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-03-31 14:26:25,342 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `grid_page_length` int(11) not null default 50
2025-03-31 14:26:25,613 WARNING database DDL Query made to DB:
ALTER TABLE `tabNumber Card` ADD COLUMN `currency` varchar(140)
2025-03-31 14:26:25,840 WARNING database DDL Query made to DB:
ALTER TABLE `tabDashboard Chart` ADD COLUMN `currency` varchar(140)
2025-03-31 14:26:26,037 WARNING database DDL Query made to DB:
ALTER TABLE `tabReport` ADD COLUMN `add_translate_data` int(1) not null default 0
2025-03-31 14:26:26,288 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Format` ADD COLUMN `pdf_generator` varchar(140) default 'wkhtmltopdf'
2025-03-31 14:26:26,826 WARNING database DDL Query made to DB:
ALTER TABLE `tabFile`
				ADD INDEX IF NOT EXISTS `file_url_index`(file_url(100))
2025-03-31 14:26:27,102 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD INDEX `last_active_index`(`last_active`)
2025-03-31 14:26:27,250 WARNING database DDL Query made to DB:
ALTER TABLE `tabSMS Parameter` MODIFY `value` varchar(255)
2025-03-31 14:26:27,549 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkflow Transition` ADD COLUMN `send_email_to_creator` int(1) not null default 0
2025-03-31 14:26:27,969 WARNING database DDL Query made to DB:
ALTER TABLE `tabList View Settings` ADD COLUMN `disable_automatic_recency_filters` int(1) not null default 0
2025-03-31 14:26:28,132 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoogle Calendar` ADD COLUMN `sync_as_public` int(1) not null default 0
2025-03-31 14:26:28,450 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebhook Request Log` MODIFY `url` text
2025-03-31 14:26:28,586 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-03-31 14:26:29,561 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-03-31 14:26:31,113 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-03-31 14:26:31,341 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-03-31 14:26:31,842 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-03-31 14:26:32,082 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-03-31 14:26:32,112 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-03-31 14:26:32,356 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-03-31 14:26:32,907 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-03-31 14:26:32,932 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-03-31 14:26:33,895 WARNING database DDL Query made to DB:
ALTER TABLE __UserSettings CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
2025-04-01 09:58:16,863 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-01 09:58:17,739 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-01 09:58:18,825 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-01 09:58:20,773 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-01 09:58:21,012 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-01 09:58:21,538 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Checkin` MODIFY `latitude` decimal(21,9) not null default 0, MODIFY `longitude` decimal(21,9) not null default 0
2025-04-01 09:58:21,733 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-01 09:58:21,937 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD COLUMN `pay_via_payment_entry` int(1) not null default 0, ADD COLUMN `expense_account` varchar(140), ADD COLUMN `payable_account` varchar(140), ADD COLUMN `posting_date` date, ADD COLUMN `paid_amount` decimal(21,9) not null default 0, ADD COLUMN `cost_center` varchar(140), ADD COLUMN `status` varchar(140)
2025-04-01 09:58:21,960 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0, MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `actual_encashable_days` decimal(21,9) not null default 0
2025-04-01 09:58:21,997 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD INDEX `creation`(`creation`)
2025-04-01 09:58:22,305 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-01 09:58:22,331 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-01 09:58:22,498 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD COLUMN `auto_update_last_sync` int(1) not null default 0
2025-04-01 09:58:22,522 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` MODIFY `working_hours_threshold_for_half_day` decimal(21,9) not null default 0, MODIFY `working_hours_threshold_for_absent` decimal(21,9) not null default 0
2025-04-01 09:58:22,560 WARNING database DDL Query made to DB:
ALTER TABLE `tabShift Type` ADD INDEX `creation`(`creation`)
2025-04-01 09:58:22,836 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal` MODIFY `self_score` decimal(21,9) not null default 0, MODIFY `total_score` decimal(21,9) not null default 0, MODIFY `avg_feedback_score` decimal(21,9) not null default 0, MODIFY `final_score` decimal(21,9) not null default 0, MODIFY `goal_score_percentage` decimal(21,9) not null default 0
2025-04-01 09:58:22,872 WARNING database DDL Query made to DB:
ALTER TABLE `tabAppraisal` ADD INDEX `creation`(`creation`)
2025-04-01 09:58:23,081 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-01 09:58:23,648 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0
2025-04-01 09:58:24,149 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-01 09:58:24,175 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-01 09:58:25,438 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` ADD COLUMN `airline` varchar(140)
2025-04-01 09:58:25,463 WARNING database DDL Query made to DB:
ALTER TABLE `tabLeave Encashment` MODIFY `actual_encashable_days` decimal(21,9) not null default 0, MODIFY `encashment_days` decimal(21,9) not null default 0, MODIFY `leave_balance` decimal(21,9) not null default 0, MODIFY `encashment_amount` decimal(21,9) not null default 0
2025-04-01 13:25:45,476 WARNING database DDL Query made to DB:
create table `tabLMS Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`billing_name` varchar(140),
`source` varchar(140),
`payment_for_document_type` varchar(140),
`payment_for_document` varchar(140),
`payment_received` int(1) not null default 0,
`payment_for_certificate` int(1) not null default 0,
`currency` varchar(140),
`amount` decimal(21,9) not null default 0,
`amount_with_gst` decimal(21,9) not null default 0,
`order_id` varchar(140),
`payment_id` varchar(140),
`address` varchar(140),
`gstin` varchar(140),
`pan` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:45,663 WARNING database DDL Query made to DB:
create table `tabLMS Enrollment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`progress` decimal(21,9) not null default 0,
`payment` varchar(140),
`current_lesson` varchar(140),
`member` varchar(140),
`member_name` varchar(140),
`member_username` varchar(140),
`purchased_certificate` int(1) not null default 0,
`certificate` varchar(140),
`cohort` varchar(140),
`subgroup` varchar(140),
`batch_old` varchar(140),
`member_type` varchar(140) default 'Student',
`role` varchar(140) default 'Member',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `course`(`course`),
index `member`(`member`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:45,830 WARNING database DDL Query made to DB:
create table `tabCourse Lesson` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`include_in_preview` int(1) not null default 0,
`chapter` varchar(140),
`is_scorm_package` int(1) not null default 0,
`course` varchar(140),
`content` text,
`body` longtext,
`instructor_content` text,
`instructor_notes` longtext,
`youtube` varchar(140),
`quiz_id` varchar(140),
`question` text,
`file_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:46,069 WARNING database DDL Query made to DB:
create table `tabLMS Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:46,200 WARNING database DDL Query made to DB:
create table `tabLMS Live Class` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`host` varchar(140),
`batch_name` varchar(140),
`event` varchar(140),
`description` text,
`date` date,
`duration` int(11) not null default 0,
`time` time(6),
`timezone` varchar(140),
`password` text,
`start_url` text,
`auto_recording` varchar(140) default 'No Recording',
`join_url` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:46,321 WARNING database DDL Query made to DB:
create table `tabLMS Certificate Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`course_title` varchar(140),
`member` varchar(140),
`member_name` varchar(140),
`evaluator` varchar(140),
`evaluator_name` varchar(140),
`batch_name` varchar(140),
`batch_title` varchar(140),
`timezone` varchar(140),
`date` date,
`day` varchar(140),
`google_meet_link` varchar(140),
`start_time` time(6),
`end_time` time(6),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:46,453 WARNING database DDL Query made to DB:
create table `tabCertification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`certification_name` varchar(140),
`organization` varchar(140),
`description` text,
`expire` int(1) not null default 0,
`issue_date` date,
`expiration_date` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:46,543 WARNING database DDL Query made to DB:
create table `tabLMS Assessment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_type` varchar(140),
`assessment_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:46,633 WARNING database DDL Query made to DB:
create table `tabFunction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`function` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:46,749 WARNING database DDL Query made to DB:
create table `tabLMS Assignment Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assignment` varchar(140),
`assignment_title` varchar(140),
`type` varchar(140),
`member` varchar(140),
`member_name` varchar(140),
`evaluator` varchar(140),
`assignment_attachment` text,
`answer` longtext,
`status` varchar(140) default 'Not Graded',
`comments` longtext,
`question` longtext,
`course` varchar(140),
`lesson` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:46,848 WARNING database DDL Query made to DB:
create table `tabLMS Sidebar Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`web_page` varchar(140),
`title` varchar(140),
`icon` varchar(140),
`route` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:46,944 WARNING database DDL Query made to DB:
create table `tabLMS Batch Enrollment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`member_username` varchar(140),
`batch` varchar(140),
`payment` varchar(140),
`source` varchar(140),
`confirmation_email_sent` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:47,045 WARNING database DDL Query made to DB:
create table `tabLMS Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:47,190 WARNING database DDL Query made to DB:
create table `tabLMS Course` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`video_link` varchar(140),
`tags` varchar(140),
`image` text,
`category` varchar(140),
`status` varchar(140) default 'In Progress',
`published` int(1) not null default 0,
`published_on` date,
`upcoming` int(1) not null default 0,
`featured` int(1) not null default 0,
`disable_self_learning` int(1) not null default 0,
`short_introduction` text,
`description` longtext,
`paid_course` int(1) not null default 0,
`enable_certification` int(1) not null default 0,
`paid_certificate` int(1) not null default 0,
`evaluator` varchar(140),
`course_price` decimal(21,9) not null default 0,
`currency` varchar(140),
`amount_usd` decimal(21,9) not null default 0,
`enrollments` int(11) not null default 0,
`lessons` int(11) not null default 0,
`rating` varchar(140) default '0',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:47,326 WARNING database DDL Query made to DB:
create table `tabLMS Question` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` longtext,
`type` varchar(140),
`multiple` int(1) not null default 0,
`option_1` text,
`is_correct_1` int(1) not null default 0,
`explanation_1` text,
`option_2` text,
`is_correct_2` int(1) not null default 0,
`explanation_2` text,
`option_3` text,
`is_correct_3` int(1) not null default 0,
`explanation_3` text,
`option_4` text,
`is_correct_4` int(1) not null default 0,
`explanation_4` text,
`possibility_1` text,
`possibility_3` text,
`possibility_2` text,
`possibility_4` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:47,444 WARNING database DDL Query made to DB:
create table `tabCourse Instructor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`instructor` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:47,531 WARNING database DDL Query made to DB:
create table `tabLesson Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lesson` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:47,628 WARNING database DDL Query made to DB:
create table `tabLMS Quiz Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`quiz` varchar(140),
`quiz_title` varchar(140),
`course` varchar(140),
`member` varchar(140),
`member_name` varchar(140),
`score` int(11) not null default 0,
`score_out_of` int(11) not null default 0,
`percentage` int(11) not null default 0,
`passing_percentage` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:47,734 WARNING database DDL Query made to DB:
create table `tabCohort Mentor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cohort` varchar(140),
`email` varchar(140),
`subgroup` varchar(140),
`course` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:47,844 WARNING database DDL Query made to DB:
create table `tabCourse Chapter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`course` varchar(140),
`course_title` varchar(140),
`is_scorm_package` int(1) not null default 0,
`scorm_package` varchar(140),
`scorm_package_path` longtext,
`manifest_file` longtext,
`launch_file` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:47,978 WARNING database DDL Query made to DB:
create table `tabLMS Badge` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`title` varchar(140) unique,
`description` text,
`image` text,
`grant_only_once` int(1) not null default 0,
`event` varchar(140),
`reference_doctype` varchar(140),
`user_field` varchar(140),
`field_to_check` varchar(140),
`condition` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:48,142 WARNING database DDL Query made to DB:
create table `tabLMS Program Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`full_name` varchar(140),
`progress` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:48,240 WARNING database DDL Query made to DB:
create table `tabLMS Timetable Legend` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`label` varchar(140),
`color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:48,344 WARNING database DDL Query made to DB:
create table `tabLMS Certificate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`evaluator` varchar(140),
`evaluator_name` varchar(140),
`issue_date` date,
`expiry_date` date,
`template` varchar(140),
`published` int(1) not null default 0,
`course` varchar(140),
`course_title` varchar(140),
`batch_name` varchar(140),
`batch_title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:48,464 WARNING database DDL Query made to DB:
create table `tabLMS Quiz Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` text,
`question_name` varchar(140),
`answer` text,
`marks` int(11) not null default 0,
`marks_out_of` int(11) not null default 0,
`is_correct` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:48,558 WARNING database DDL Query made to DB:
create table `tabCohort Join Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cohort` varchar(140),
`email` varchar(140),
`subgroup` varchar(140),
`status` varchar(140) default 'Pending',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:48,664 WARNING database DDL Query made to DB:
create table `tabLMS Mentor Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`course` varchar(140),
`reviewed_by` varchar(140),
`member_name` varchar(140),
`status` varchar(140),
`comments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:48,771 WARNING database DDL Query made to DB:
create table `tabCohort Web Page` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`slug` varchar(140),
`title` varchar(140),
`template` varchar(140),
`scope` varchar(140) default 'Cohort',
`required_role` varchar(140) default 'Public',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:48,985 WARNING database DDL Query made to DB:
create table `tabCohort Staff` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cohort` varchar(140),
`course` varchar(140),
`email` varchar(140),
`role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:49,090 WARNING database DDL Query made to DB:
create table `tabLMS Exercise` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`description` text,
`code` longtext,
`answer` longtext,
`course` varchar(140),
`hints` text,
`tests` longtext,
`image` longtext,
`lesson` varchar(140),
`index_` int(11) not null default 0,
`index_label` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:49,188 WARNING database DDL Query made to DB:
create table `tabLMS Program Course` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`course_title` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:49,306 WARNING database DDL Query made to DB:
create table `tabLMS Course Progress` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`status` varchar(140),
`lesson` varchar(140),
`chapter` varchar(140),
`course` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `member`(`member`),
index `status`(`status`),
index `lesson`(`lesson`),
index `chapter`(`chapter`),
index `course`(`course`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:49,415 WARNING database DDL Query made to DB:
create table `tabPayment Country` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:49,509 WARNING database DDL Query made to DB:
create table `tabLMS Timetable Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:49,610 WARNING database DDL Query made to DB:
create table `tabLMS Quiz Question` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` varchar(140),
`marks` int(11) not null default 1,
`question_detail` text,
`type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:49,719 WARNING database DDL Query made to DB:
create table `tabLMS Certificate Evaluation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`course` varchar(140),
`batch_name` varchar(140),
`evaluator` varchar(140),
`evaluator_name` varchar(140),
`date` date,
`start_time` time(6),
`end_time` time(6),
`rating` decimal(3,2),
`status` varchar(140),
`summary` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:49,824 WARNING database DDL Query made to DB:
create table `tabLMS Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`question` longtext,
`type` varchar(140),
`grade_assignment` int(1) not null default 1,
`show_answer` int(1) not null default 0,
`answer` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:49,943 WARNING database DDL Query made to DB:
create table `tabLMS Program` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:50,061 WARNING database DDL Query made to DB:
create table `tabWork Experience` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`company` varchar(140),
`location` varchar(140),
`description` text,
`current` int(1) not null default 0,
`from_date` date,
`to_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:50,148 WARNING database DDL Query made to DB:
create sequence if not exists evaluator_schedule_id_seq nocache nocycle
2025-04-01 13:25:50,167 WARNING database DDL Query made to DB:
create table `tabEvaluator Schedule` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:50,284 WARNING database DDL Query made to DB:
create table `tabLMS Quiz` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`max_attempts` int(11) not null default 0,
`show_answers` int(1) not null default 1,
`show_submission_history` int(1) not null default 0,
`total_marks` int(11) not null default 0,
`passing_percentage` int(11) not null default 0,
`duration` varchar(140),
`shuffle_questions` int(1) not null default 0,
`limit_questions_to` int(11) not null default 0,
`lesson` varchar(140),
`course` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:50,421 WARNING database DDL Query made to DB:
create table `tabCohort` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`title` varchar(140),
`slug` varchar(140) unique,
`instructor` varchar(140),
`status` varchar(140),
`begin_date` date,
`end_date` date,
`duration` varchar(140),
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:50,528 WARNING database DDL Query made to DB:
create table `tabLMS Batch Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`member_image` text,
`batch` varchar(140),
`content` decimal(3,2),
`instructors` decimal(3,2),
`value` decimal(3,2),
`feedback` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:50,650 WARNING database DDL Query made to DB:
create table `tabLMS Course Mentor Mapping` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`mentor` varchar(140),
`mentor_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:50,767 WARNING database DDL Query made to DB:
create table `tabCourse Evaluator` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`evaluator` varchar(140) unique,
`full_name` varchar(140),
`user_image` text,
`username` varchar(140),
`unavailable_from` date,
`unavailable_to` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:50,888 WARNING database DDL Query made to DB:
create table `tabChapter Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`chapter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:50,981 WARNING database DDL Query made to DB:
create table `tabCohort Subgroup` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cohort` varchar(140),
`slug` varchar(140),
`title` varchar(140),
`invite_code` varchar(140),
`course` varchar(140),
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:51,076 WARNING database DDL Query made to DB:
create table `tabPreferred Function` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`function` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:51,169 WARNING database DDL Query made to DB:
create table `tabUser Skill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:51,272 WARNING database DDL Query made to DB:
create table `tabScheduled Flow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lesson` varchar(140),
`lesson_title` varchar(140),
`date` date,
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:51,361 WARNING database DDL Query made to DB:
create table `tabRelated Courses` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:51,460 WARNING database DDL Query made to DB:
create table `tabLMS Badge Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`issued_on` date,
`badge` varchar(140),
`badge_image` text,
`badge_description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:51,600 WARNING database DDL Query made to DB:
create table `tabExercise Latest Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exercise` varchar(140),
`status` varchar(140),
`batch_old` varchar(140),
`exercise_title` varchar(140),
`course` varchar(140),
`lesson` varchar(140),
`solution` longtext,
`image` longtext,
`test_results` text,
`comments` text,
`latest_submission` varchar(140),
`member` varchar(140),
`member_email` varchar(140),
`member_cohort` varchar(140),
`member_subgroup` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `exercise`(`exercise`),
index `member_email`(`member_email`),
index `member_cohort`(`member_cohort`),
index `member_subgroup`(`member_subgroup`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:51,721 WARNING database DDL Query made to DB:
create table `tabLMS Course Review` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`rating` decimal(3,2),
`course` varchar(140),
`review` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:51,838 WARNING database DDL Query made to DB:
create table `tabExercise Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exercise` varchar(140),
`status` varchar(140),
`batch_old` varchar(140),
`exercise_title` varchar(140),
`course` varchar(140),
`lesson` varchar(140),
`solution` longtext,
`image` longtext,
`test_results` text,
`comments` text,
`member` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:51,938 WARNING database DDL Query made to DB:
create table `tabSkills` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:52,020 WARNING database DDL Query made to DB:
create table `tabLMS Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`option` varchar(140),
`is_correct` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:52,165 WARNING database DDL Query made to DB:
create table `tabLMS Batch` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`start_date` date,
`end_date` date,
`start_time` time(6),
`end_time` time(6),
`timezone` varchar(140),
`published` int(1) not null default 0,
`allow_self_enrollment` int(1) not null default 0,
`certification` int(1) not null default 0,
`description` text,
`medium` varchar(140) default 'Online',
`category` varchar(140),
`confirmation_email_template` varchar(140),
`seat_count` int(11) not null default 0,
`evaluation_end_date` date,
`meta_image` text,
`batch_details` longtext,
`batch_details_raw` longtext,
`timetable_template` varchar(140),
`show_live_class` int(1) not null default 0,
`allow_future` int(1) not null default 1,
`paid_batch` int(1) not null default 0,
`amount` decimal(21,9) not null default 0,
`currency` varchar(140),
`amount_usd` decimal(21,9) not null default 0,
`custom_component` longtext,
`custom_script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:52,274 WARNING database DDL Query made to DB:
create sequence if not exists batch_course_id_seq nocache nocycle
2025-04-01 13:25:52,294 WARNING database DDL Query made to DB:
create table `tabBatch Course` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`title` varchar(140),
`evaluator` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:52,394 WARNING database DDL Query made to DB:
create table `tabLMS Course Interest` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`user` varchar(140),
`email_sent` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:52,531 WARNING database DDL Query made to DB:
create table `tabIndustry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`industry` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:52,643 WARNING database DDL Query made to DB:
create table `tabEducation Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`institution_name` varchar(140),
`location` varchar(140),
`degree_type` varchar(140),
`major` varchar(140),
`grade_type` varchar(140),
`grade` varchar(140),
`start_date` date,
`end_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:52,750 WARNING database DDL Query made to DB:
create table `tabLMS Batch Old` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`start_date` date,
`start_time` time(6),
`title` varchar(140),
`sessions_on` varchar(140),
`end_time` time(6),
`description` longtext,
`visibility` varchar(140) default 'Public',
`membership` varchar(140),
`status` varchar(140) default 'Active',
`stage` varchar(140) default 'Ready',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:52,851 WARNING database DDL Query made to DB:
create table `tabPreferred Industry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`industry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:52,952 WARNING database DDL Query made to DB:
create table `tabLMS Batch Timetable` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`date` date,
`day` int(11) not null default 0,
`start_time` time(6),
`end_time` time(6),
`duration` varchar(140),
`milestone` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:53,053 WARNING database DDL Query made to DB:
create table `tabInvite Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`invite_email` varchar(140) unique,
`signup_email` varchar(140),
`status` varchar(140) default 'Pending',
`full_name` varchar(140),
`username` varchar(140),
`invite_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:53,557 WARNING database DDL Query made to DB:
create table `tabLMS Job Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`resume` text,
`job` varchar(140),
`job_title` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:53,691 WARNING database DDL Query made to DB:
create table `tabJob Opportunity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`job_title` varchar(140),
`location` varchar(140),
`disabled` int(1) not null default 0,
`type` varchar(140) default 'Full Time',
`status` varchar(140) default 'Open',
`description` longtext,
`company_name` varchar(140),
`company_website` varchar(140),
`company_logo` text,
`company_email_address` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-04-01 13:25:56,388 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `country` varchar(140)
2025-04-01 13:25:56,511 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `verify_terms` int(1) not null default 0
2025-04-01 13:25:56,623 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `user_category` varchar(140)
2025-04-01 13:25:56,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `cover_image` text
2025-04-01 13:25:56,905 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `looking_for_job` int(1) not null default 0
2025-04-01 13:25:57,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `linkedin` varchar(140)
2025-04-01 13:25:57,193 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `github` varchar(140)
2025-04-01 13:25:57,341 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `medium` varchar(140)
2025-04-01 13:25:57,489 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `profession` varchar(140)
2025-04-01 13:25:57,635 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `hide_private` int(1) not null default 0
2025-04-01 13:25:58,804 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `preferred_location` varchar(140)
2025-04-01 13:25:59,114 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `dream_companies` varchar(140)
2025-04-01 13:25:59,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `attire` varchar(140)
2025-04-01 13:25:59,494 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `collaboration` varchar(140)
2025-04-01 13:25:59,622 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `role` varchar(140)
2025-04-01 13:25:59,843 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `location_preference` varchar(140)
2025-04-01 13:26:00,005 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `time` varchar(140)
2025-04-01 13:26:00,128 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `company_type` varchar(140)
2025-04-01 13:26:00,254 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `headline` varchar(140)
2025-04-01 13:26:00,374 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `city` varchar(140)
2025-04-01 13:26:00,500 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `college` varchar(140)
2025-04-01 13:26:00,645 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `branch` varchar(140)
2025-04-01 13:26:10,377 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-01 13:26:11,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-01 13:26:12,545 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-01 13:26:14,627 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-01 13:26:14,898 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-01 13:26:15,404 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-01 13:26:15,664 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140) default 'Present', MODIFY `working_hours` decimal(21,9) not null default 0
2025-04-01 13:26:15,696 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-01 13:26:15,971 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-01 13:26:16,551 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-01 13:26:16,575 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-01 14:54:13,168 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-01 14:54:13,641 WARNING database DDL Query made to DB:
ALTER TABLE `tabDocType` ADD COLUMN `row_format` varchar(140) default 'Dynamic'
2025-04-01 14:54:13,859 WARNING database DDL Query made to DB:
ALTER TABLE `tabWorkspace Shortcut` ADD COLUMN `report_ref_doctype` varchar(140)
2025-04-01 14:54:14,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Page View` ADD INDEX `path_index`(`path`)
2025-04-01 14:54:15,171 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmail Account` ADD COLUMN `always_bcc` varchar(140)
2025-04-01 14:54:15,419 WARNING database DDL Query made to DB:
ALTER TABLE `tabTag Link`
				ADD INDEX IF NOT EXISTS `document_type_document_name_index`(document_type, document_name)
2025-04-01 14:54:15,590 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-01 14:54:16,450 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-01 14:54:18,120 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-01 14:54:18,338 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-01 14:54:18,867 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-01 14:54:19,099 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-01 14:54:19,132 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-01 14:54:19,322 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `quantity` decimal(21,9) not null default 0, MODIFY `expense_amount` decimal(21,9) not null default 0
2025-04-01 14:54:19,834 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-01 14:54:19,864 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-01 14:54:20,220 WARNING database DDL Query made to DB:
ALTER TABLE `tabPhysical Verification` MODIFY `total_charges` decimal(21,9) not null default 0
2025-04-01 14:54:35,254 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-01 14:54:36,004 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-01 14:54:36,905 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-01 14:54:38,398 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-01 14:54:38,599 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-01 14:54:39,078 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-04-01 14:54:39,311 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `working_hours` decimal(21,9) not null default 0, MODIFY `status` varchar(140) default 'Present'
2025-04-01 14:54:39,340 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD INDEX `status_index`(`status`)
2025-04-01 14:54:39,567 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
2025-04-01 14:54:39,847 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-04-01 14:54:39,873 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status_index`
2025-04-01 16:16:13,583 WARNING database DDL Query made to DB:
alter table `tabDocType` add column if not exists migration_hash varchar(140)
2025-04-01 16:16:14,281 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` DROP INDEX `print_heading`
2025-04-01 16:16:15,198 WARNING database DDL Query made to DB:
ALTER TABLE `tabPrint Heading` ADD UNIQUE INDEX IF NOT EXISTS print_heading (`print_heading`)
2025-04-01 16:16:17,009 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `fuel_qty` decimal(21,9) not null default 0, MODIFY `price` decimal(21,9) not null default 0
2025-04-01 16:16:17,210 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0, MODIFY `quantity` decimal(21,9) not null default 0
