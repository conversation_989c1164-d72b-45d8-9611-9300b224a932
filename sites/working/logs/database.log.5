2025-05-26 09:00:09,082 WARNING database DDL Query made to DB:
create table `tabMulti-Dimensional Budget Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`accounting_dimension` varchar(140),
`accounting_dimension_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:10,981 WARNING database DDL Query made to DB:
create table `tabAssessment Plan` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student_group` varchar(140),
`assessment_name` varchar(140),
`assessment_group` varchar(140),
`grading_scale` varchar(140),
`program` varchar(140),
`course` varchar(140),
`academic_year` varchar(140),
`academic_term` varchar(140),
`schedule_date` date,
`room` varchar(140),
`examiner` varchar(140),
`examiner_name` varchar(140),
`from_time` time(6),
`to_time` time(6),
`supervisor` varchar(140),
`supervisor_name` varchar(140),
`maximum_assessment_score` decimal(21,9) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:11,074 WARNING database DDL Query made to DB:
create table `tabProgram Enrollment Course` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`course_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:11,164 WARNING database DDL Query made to DB:
create table `tabProgram Fee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`academic_term` varchar(140),
`fee_structure` varchar(140),
`student_category` varchar(140),
`due_date` date,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:11,279 WARNING database DDL Query made to DB:
create table `tabFee Structure` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`program` varchar(140),
`student_category` varchar(140),
`academic_year` varchar(140),
`academic_term` varchar(140),
`total_amount` decimal(21,9) not null default 0,
`receivable_account` varchar(140),
`income_account` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`cost_center` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `program`(`program`),
index `academic_term`(`academic_term`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:11,376 WARNING database DDL Query made to DB:
create table `tabAssessment Criteria Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_criteria_group` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:11,484 WARNING database DDL Query made to DB:
create table `tabOptions` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`option` text,
`is_correct` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:11,597 WARNING database DDL Query made to DB:
create table `tabAssessment Plan Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_criteria` varchar(140),
`maximum_score` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:11,722 WARNING database DDL Query made to DB:
create table `tabAcademic Term` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`academic_year` varchar(140),
`term_name` varchar(140),
`term_start_date` date,
`term_end_date` date,
`title` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `term_start_date`(`term_start_date`),
index `term_end_date`(`term_end_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:11,902 WARNING database DDL Query made to DB:
ALTER TABLE `tabCourse` ADD COLUMN `course_name` varchar(140) unique, ADD COLUMN `department` varchar(140), ADD COLUMN `hero_image` text, ADD COLUMN `default_grading_scale` varchar(140)
2025-05-26 09:00:11,904 WARNING database DDL Query made to DB:
ALTER TABLE `tabCourse` ADD UNIQUE INDEX IF NOT EXISTS course_name (`course_name`)
2025-05-26 09:00:12,035 WARNING database DDL Query made to DB:
create table `tabCourse Assessment Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_criteria` varchar(140),
`weightage` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:12,213 WARNING database DDL Query made to DB:
ALTER TABLE `tabStudent` ADD COLUMN `enabled` int(1) not null default 1, ADD COLUMN `first_name` varchar(140), ADD COLUMN `middle_name` varchar(140), ADD COLUMN `last_name` varchar(140), ADD COLUMN `naming_series` varchar(140), ADD COLUMN `joining_date` date, ADD COLUMN `user` varchar(140), ADD COLUMN `student_applicant` varchar(140), ADD COLUMN `image` text, ADD COLUMN `student_email_id` varchar(140) unique, ADD COLUMN `date_of_birth` date, ADD COLUMN `blood_group` varchar(140), ADD COLUMN `student_mobile_number` varchar(140), ADD COLUMN `gender` varchar(140), ADD COLUMN `nationality` varchar(140), ADD COLUMN `address_line_1` varchar(140), ADD COLUMN `address_line_2` varchar(140), ADD COLUMN `pincode` varchar(140), ADD COLUMN `city` varchar(140), ADD COLUMN `state` varchar(140), ADD COLUMN `country` varchar(140), ADD COLUMN `date_of_leaving` date, ADD COLUMN `leaving_certificate_number` varchar(140), ADD COLUMN `reason_for_leaving` text, ADD COLUMN `student_name` varchar(140)
2025-05-26 09:00:12,215 WARNING database DDL Query made to DB:
ALTER TABLE `tabStudent` ADD UNIQUE INDEX IF NOT EXISTS student_email_id (`student_email_id`)
2025-05-26 09:00:12,390 WARNING database DDL Query made to DB:
create table `tabFees` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`student` varchar(140),
`student_name` varchar(140),
`include_payment` int(1) not null default 0,
`send_payment_request` int(1) not null default 0,
`company` varchar(140),
`posting_date` date,
`posting_time` time(6),
`due_date` date,
`set_posting_time` int(1) not null default 0,
`program_enrollment` varchar(140),
`program` varchar(140),
`academic_year` varchar(140),
`academic_term` varchar(140),
`fee_structure` varchar(140),
`fee_schedule` varchar(140),
`currency` varchar(140),
`amended_from` varchar(140),
`grand_total` decimal(21,9) not null default 0,
`grand_total_in_words` varchar(240),
`outstanding_amount` decimal(21,9) not null default 0,
`contact_email` varchar(140),
`student_category` varchar(140),
`student_batch` varchar(140),
`receivable_account` varchar(140),
`income_account` varchar(140),
`cost_center` varchar(140),
`letter_head` varchar(140),
`select_print_heading` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `posting_date`(`posting_date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:12,505 WARNING database DDL Query made to DB:
create table `tabFee Schedule Program` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`program` varchar(140),
`student_batch` varchar(140),
`total_students` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:12,591 WARNING database DDL Query made to DB:
create table `tabFee Component` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fees_category` varchar(140),
`description` text,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:12,679 WARNING database DDL Query made to DB:
create table `tabAcademic Year` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`academic_year_name` varchar(140) unique,
`year_start_date` date,
`year_end_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:12,797 WARNING database DDL Query made to DB:
create table `tabCourse Topic` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`topic` varchar(140),
`topic_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:12,887 WARNING database DDL Query made to DB:
create table `tabQuiz Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enrollment` varchar(140),
`student` varchar(140),
`course` varchar(140),
`quiz` varchar(140),
`status` varchar(140),
`activity_date` varchar(140),
`score` varchar(140),
`time_taken` decimal(21,9),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:12,981 WARNING database DDL Query made to DB:
create table `tabStudent Sibling` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`studying_in_same_institute` varchar(140),
`full_name` varchar(140),
`gender` varchar(140),
`student` varchar(140),
`institution` varchar(140),
`program` varchar(140),
`date_of_birth` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:13,117 WARNING database DDL Query made to DB:
create table `tabGrading Scale Interval` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`grade_code` varchar(140),
`threshold` decimal(21,9) not null default 0,
`grade_description` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:13,215 WARNING database DDL Query made to DB:
create table `tabQuiz Question` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question_link` varchar(140),
`question` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:13,353 WARNING database DDL Query made to DB:
create table `tabStudent Language` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`language_name` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:13,441 WARNING database DDL Query made to DB:
create table `tabStudent Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:13,526 WARNING database DDL Query made to DB:
create table `tabProgram Course` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`course_name` varchar(140),
`required` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:13,700 WARNING database DDL Query made to DB:
create table `tabStudent Group Instructor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`instructor` varchar(140),
`instructor_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:13,795 WARNING database DDL Query made to DB:
create table `tabStudent Leave Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student` varchar(140),
`student_name` varchar(140),
`from_date` date,
`to_date` date,
`total_leave_days` decimal(21,9) not null default 0,
`attendance_based_on` varchar(140) default 'Student Group',
`student_group` varchar(140),
`course_schedule` varchar(140),
`mark_as_present` int(1) not null default 0,
`reason` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:13,894 WARNING database DDL Query made to DB:
create table `tabInstructor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`instructor_name` varchar(140),
`employee` varchar(140),
`gender` varchar(140),
`status` varchar(140) default 'Active',
`naming_series` varchar(140),
`department` varchar(140),
`image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:13,982 WARNING database DDL Query made to DB:
create table `tabTopic Content` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`content_type` varchar(140),
`content` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:14,190 WARNING database DDL Query made to DB:
create table `tabFee Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`fee_structure` varchar(140),
`posting_date` date,
`due_date` date,
`naming_series` varchar(140),
`fee_creation_status` varchar(140),
`send_email` int(1) not null default 0,
`student_category` varchar(140),
`program` varchar(140),
`academic_year` varchar(140),
`academic_term` varchar(140),
`currency` varchar(140),
`total_amount` decimal(21,9) not null default 0,
`grand_total` decimal(21,9) not null default 0,
`grand_total_in_words` varchar(240),
`letter_head` varchar(140),
`select_print_heading` varchar(140),
`receivable_account` varchar(140),
`income_account` varchar(140),
`company` varchar(140),
`amended_from` varchar(140),
`cost_center` varchar(140),
`error_log` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:14,303 WARNING database DDL Query made to DB:
create table `tabAssessment Criteria` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_criteria` varchar(140),
`assessment_criteria_group` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:14,404 WARNING database DDL Query made to DB:
create table `tabStudent Admission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140) unique,
`published` int(1) not null default 0,
`enable_admission_application` int(1) not null default 0,
`academic_year` varchar(140),
`admission_start_date` date,
`admission_end_date` date,
`introduction` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:14,522 WARNING database DDL Query made to DB:
create table `tabStudent Guardian` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`guardian` varchar(140),
`guardian_name` varchar(140),
`relation` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:14,652 WARNING database DDL Query made to DB:
create table `tabStudent Applicant` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`naming_series` varchar(140),
`application_date` date,
`application_status` varchar(140),
`program` varchar(140),
`student_email_id` varchar(140) unique,
`student_admission` varchar(140),
`student_category` varchar(140),
`image` text,
`academic_year` varchar(140),
`academic_term` varchar(140),
`paid` int(1) not null default 0,
`title` varchar(140),
`amended_from` varchar(140),
`date_of_birth` date,
`gender` varchar(140),
`blood_group` varchar(140),
`student_mobile_number` varchar(140),
`nationality` varchar(140),
`address_line_1` varchar(140),
`address_line_2` varchar(140),
`city` varchar(140),
`state` varchar(140),
`pincode` varchar(140),
`country` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:14,767 WARNING database DDL Query made to DB:
create table `tabFee Schedule Student Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student_group` varchar(140),
`total_students` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:14,864 WARNING database DDL Query made to DB:
create table `tabTopic` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`topic_name` varchar(140) unique,
`description` text,
`hero_image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:14,952 WARNING database DDL Query made to DB:
create table `tabProgram Enrollment Fee` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`academic_term` varchar(140),
`fee_structure` varchar(140),
`due_date` date,
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:15,056 WARNING database DDL Query made to DB:
create table `tabCourse Schedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student_group` varchar(140),
`instructor` varchar(140),
`instructor_name` varchar(140),
`naming_series` varchar(140),
`program` varchar(140),
`course` varchar(140),
`color` varchar(140),
`schedule_date` date,
`room` varchar(140),
`from_time` time(6),
`to_time` time(6),
`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:15,159 WARNING database DDL Query made to DB:
create table `tabGuardian` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`guardian_name` varchar(140),
`email_address` varchar(140),
`mobile_number` varchar(140),
`alternate_number` varchar(140),
`date_of_birth` date,
`user` varchar(140),
`education` varchar(140),
`occupation` varchar(140),
`designation` varchar(140),
`work_address` text,
`image` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:15,252 WARNING database DDL Query made to DB:
create table `tabAssessment Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_group_name` varchar(140) unique,
`parent_assessment_group` varchar(140),
`is_group` int(1) not null default 0,
`lft` int(11) not null default 0,
`rgt` int(11) not null default 0,
`old_parent` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:15,341 WARNING database DDL Query made to DB:
create table `tabStudent Group Student` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student` varchar(140),
`student_name` varchar(140),
`group_roll_number` int(11) not null default 0,
`active` int(1) not null default 1,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:15,486 WARNING database DDL Query made to DB:
create table `tabStudent Batch Name` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`batch_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:15,629 WARNING database DDL Query made to DB:
create table `tabFee Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category_name` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:15,756 WARNING database DDL Query made to DB:
create table `tabStudent Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student` varchar(140),
`student_name` varchar(140),
`type` varchar(140),
`date` date,
`academic_year` varchar(140),
`academic_term` varchar(140),
`program` varchar(140),
`student_batch` varchar(140),
`log` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:15,929 WARNING database DDL Query made to DB:
create table `tabQuestion` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` longtext,
`question_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:16,024 WARNING database DDL Query made to DB:
create table `tabGrading Scale` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`grading_scale_name` varchar(140) unique,
`amended_from` varchar(140),
`description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:16,140 WARNING database DDL Query made to DB:
create table `tabQuiz` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`passing_score` decimal(21,9) not null default 75.0,
`max_attempts` int(11) not null default 1,
`grading_basis` varchar(140) default 'Latest Highest Score',
`is_time_bound` int(1) not null default 0,
`duration` decimal(21,9),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:16,282 WARNING database DDL Query made to DB:
create table `tabCourse Enrollment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`program_enrollment` varchar(140),
`program` varchar(140),
`enrollment_date` date,
`course` varchar(140),
`student` varchar(140),
`student_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:16,374 WARNING database DDL Query made to DB:
create table `tabQuiz Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` varchar(140),
`selected_option` varchar(140),
`quiz_result` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:16,499 WARNING database DDL Query made to DB:
create table `tabProgram` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`program_name` varchar(140) unique,
`department` varchar(140),
`hero_image` text,
`program_abbreviation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:16,635 WARNING database DDL Query made to DB:
create table `tabInstructor Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`academic_year` varchar(140),
`academic_term` varchar(140),
`department` varchar(140),
`program` varchar(140),
`course` varchar(140),
`student_group` varchar(140),
`other_details` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:16,851 WARNING database DDL Query made to DB:
create table `tabAssessment Result Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_criteria` varchar(140),
`maximum_score` decimal(21,9) not null default 0,
`score` decimal(21,9) not null default 0,
`grade` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:16,957 WARNING database DDL Query made to DB:
create table `tabStudent Group Creation Tool Course` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`group_based_on` varchar(140),
`course` varchar(140),
`batch` varchar(140),
`student_group_name` varchar(140),
`course_code` varchar(140),
`max_strength` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:17,060 WARNING database DDL Query made to DB:
create table `tabProgram Enrollment Tool Student` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student_applicant` varchar(140),
`student` varchar(140),
`student_name` varchar(140),
`student_batch_name` varchar(140),
`student_category` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:17,224 WARNING database DDL Query made to DB:
create table `tabGuardian Student` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student` varchar(140),
`student_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:17,313 WARNING database DDL Query made to DB:
create table `tabGuardian Interest` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`interest` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:17,419 WARNING database DDL Query made to DB:
create table `tabProgram Enrollment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student` varchar(140),
`student_name` varchar(140),
`enrollment_date` date,
`program` varchar(140),
`academic_year` varchar(140),
`academic_term` varchar(140),
`image` text,
`student_category` varchar(140),
`student_batch_name` varchar(140),
`school_house` varchar(140),
`boarding_student` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:17,562 WARNING database DDL Query made to DB:
create table `tabArticle` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`author` varchar(140),
`content` longtext,
`publish_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:17,671 WARNING database DDL Query made to DB:
create table `tabStudent Attendance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140),
`student` varchar(140),
`student_name` varchar(140),
`student_mobile_number` varchar(140),
`course_schedule` varchar(140),
`student_group` varchar(140),
`date` date,
`status` varchar(140) default 'Present',
`leave_application` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `student`(`student`),
index `date`(`date`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:17,803 WARNING database DDL Query made to DB:
ALTER TABLE `tabRoom` ADD COLUMN `room_name` varchar(140), ADD COLUMN `room_number` varchar(140), ADD COLUMN `seating_capacity` varchar(140)
2025-05-26 09:00:17,886 WARNING database DDL Query made to DB:
create table `tabSchool House` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`house_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:17,977 WARNING database DDL Query made to DB:
create table `tabStudent Admission Program` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`program` varchar(140),
`min_age` int(11) not null default 0,
`max_age` int(11) not null default 0,
`description` text,
`application_fee` decimal(21,9) not null default 0,
`applicant_naming_series` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:18,153 WARNING database DDL Query made to DB:
create table `tabAssessment Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_plan` varchar(140),
`program` varchar(140),
`course` varchar(140),
`academic_year` varchar(140),
`academic_term` varchar(140),
`student` varchar(140),
`student_name` varchar(140),
`student_group` varchar(140),
`assessment_group` varchar(140),
`grading_scale` varchar(140),
`maximum_score` decimal(21,9) not null default 0,
`total_score` decimal(21,9) not null default 0,
`grade` varchar(140),
`comment` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:18,252 WARNING database DDL Query made to DB:
create table `tabStudent Group` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`academic_year` varchar(140),
`group_based_on` varchar(140),
`student_group_name` varchar(140) unique,
`max_strength` int(11) not null default 0,
`academic_term` varchar(140),
`program` varchar(140),
`batch` varchar(140),
`student_category` varchar(140),
`course` varchar(140),
`disabled` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:18,349 WARNING database DDL Query made to DB:
create table `tabCourse Activity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enrollment` varchar(140),
`course` varchar(140),
`student` varchar(140),
`content_type` varchar(140),
`content` varchar(140),
`activity_date` datetime(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:18,442 WARNING database DDL Query made to DB:
create table `tabStudent Siblings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140),
`gender` varchar(140),
`date_of_birth` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:19,753 WARNING database DDL Query made to DB:
create table `tabLMS Payment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`billing_name` varchar(140),
`source` varchar(140),
`payment_for_document_type` varchar(140),
`payment_for_document` varchar(140),
`payment_received` int(1) not null default 0,
`payment_for_certificate` int(1) not null default 0,
`currency` varchar(140),
`amount` decimal(21,9) not null default 0,
`amount_with_gst` decimal(21,9) not null default 0,
`order_id` varchar(140),
`payment_id` varchar(140),
`address` varchar(140),
`gstin` varchar(140),
`pan` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:19,880 WARNING database DDL Query made to DB:
create table `tabLMS Enrollment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`progress` decimal(21,9) not null default 0,
`payment` varchar(140),
`current_lesson` varchar(140),
`member` varchar(140),
`member_name` varchar(140),
`member_username` varchar(140),
`purchased_certificate` int(1) not null default 0,
`certificate` varchar(140),
`cohort` varchar(140),
`subgroup` varchar(140),
`batch_old` varchar(140),
`member_type` varchar(140) default 'Student',
`role` varchar(140) default 'Member',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `course`(`course`),
index `member`(`member`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:20,053 WARNING database DDL Query made to DB:
create table `tabCourse Lesson` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`include_in_preview` int(1) not null default 0,
`chapter` varchar(140),
`is_scorm_package` int(1) not null default 0,
`course` varchar(140),
`content` text,
`body` longtext,
`instructor_content` text,
`instructor_notes` longtext,
`youtube` varchar(140),
`quiz_id` varchar(140),
`question` text,
`file_type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:20,172 WARNING database DDL Query made to DB:
create table `tabLMS Category` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`category` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:20,301 WARNING database DDL Query made to DB:
create table `tabLMS Live Class` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`host` varchar(140),
`batch_name` varchar(140),
`event` varchar(140),
`description` text,
`date` date,
`duration` int(11) not null default 0,
`time` time(6),
`timezone` varchar(140),
`password` text,
`start_url` text,
`auto_recording` varchar(140) default 'No Recording',
`join_url` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:20,415 WARNING database DDL Query made to DB:
create table `tabLMS Certificate Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`course_title` varchar(140),
`member` varchar(140),
`member_name` varchar(140),
`evaluator` varchar(140),
`evaluator_name` varchar(140),
`batch_name` varchar(140),
`batch_title` varchar(140),
`timezone` varchar(140),
`date` date,
`day` varchar(140),
`google_meet_link` varchar(140),
`start_time` time(6),
`end_time` time(6),
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:20,521 WARNING database DDL Query made to DB:
create table `tabCertification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`certification_name` varchar(140),
`organization` varchar(140),
`description` text,
`expire` int(1) not null default 0,
`issue_date` date,
`expiration_date` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:20,625 WARNING database DDL Query made to DB:
create table `tabLMS Assessment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assessment_type` varchar(140),
`assessment_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:20,733 WARNING database DDL Query made to DB:
create table `tabFunction` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`function` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:20,889 WARNING database DDL Query made to DB:
create table `tabLMS Assignment Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`assignment` varchar(140),
`assignment_title` varchar(140),
`type` varchar(140),
`member` varchar(140),
`member_name` varchar(140),
`evaluator` varchar(140),
`assignment_attachment` text,
`answer` longtext,
`status` varchar(140) default 'Not Graded',
`comments` longtext,
`question` longtext,
`course` varchar(140),
`lesson` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:20,983 WARNING database DDL Query made to DB:
create table `tabLMS Sidebar Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`web_page` varchar(140),
`title` varchar(140),
`icon` varchar(140),
`route` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:21,071 WARNING database DDL Query made to DB:
create table `tabLMS Batch Enrollment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`member_username` varchar(140),
`batch` varchar(140),
`payment` varchar(140),
`source` varchar(140),
`confirmation_email_sent` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:21,239 WARNING database DDL Query made to DB:
create table `tabLMS Source` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`source` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:21,375 WARNING database DDL Query made to DB:
create table `tabLMS Course` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`video_link` varchar(140),
`tags` varchar(140),
`image` text,
`category` varchar(140),
`status` varchar(140) default 'In Progress',
`published` int(1) not null default 0,
`published_on` date,
`upcoming` int(1) not null default 0,
`featured` int(1) not null default 0,
`disable_self_learning` int(1) not null default 0,
`short_introduction` text,
`description` longtext,
`paid_course` int(1) not null default 0,
`enable_certification` int(1) not null default 0,
`paid_certificate` int(1) not null default 0,
`evaluator` varchar(140),
`course_price` decimal(21,9) not null default 0,
`currency` varchar(140),
`amount_usd` decimal(21,9) not null default 0,
`enrollments` int(11) not null default 0,
`lessons` int(11) not null default 0,
`rating` varchar(140) default '0',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:21,570 WARNING database DDL Query made to DB:
create table `tabLMS Question` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` longtext,
`type` varchar(140),
`multiple` int(1) not null default 0,
`option_1` text,
`is_correct_1` int(1) not null default 0,
`explanation_1` text,
`option_2` text,
`is_correct_2` int(1) not null default 0,
`explanation_2` text,
`option_3` text,
`is_correct_3` int(1) not null default 0,
`explanation_3` text,
`option_4` text,
`is_correct_4` int(1) not null default 0,
`explanation_4` text,
`possibility_1` text,
`possibility_3` text,
`possibility_2` text,
`possibility_4` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:21,712 WARNING database DDL Query made to DB:
create table `tabCourse Instructor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`instructor` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:21,794 WARNING database DDL Query made to DB:
create table `tabLesson Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lesson` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:21,886 WARNING database DDL Query made to DB:
create table `tabLMS Quiz Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`quiz` varchar(140),
`quiz_title` varchar(140),
`course` varchar(140),
`member` varchar(140),
`member_name` varchar(140),
`score` int(11) not null default 0,
`score_out_of` int(11) not null default 0,
`percentage` int(11) not null default 0,
`passing_percentage` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:21,981 WARNING database DDL Query made to DB:
create table `tabCohort Mentor` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cohort` varchar(140),
`email` varchar(140),
`subgroup` varchar(140),
`course` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:22,113 WARNING database DDL Query made to DB:
create table `tabCourse Chapter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`course` varchar(140),
`course_title` varchar(140),
`is_scorm_package` int(1) not null default 0,
`scorm_package` varchar(140),
`scorm_package_path` longtext,
`manifest_file` longtext,
`launch_file` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:22,277 WARNING database DDL Query made to DB:
create table `tabLMS Badge` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enabled` int(1) not null default 1,
`title` varchar(140) unique,
`description` text,
`image` text,
`grant_only_once` int(1) not null default 0,
`event` varchar(140),
`reference_doctype` varchar(140),
`user_field` varchar(140),
`field_to_check` varchar(140),
`condition` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:22,430 WARNING database DDL Query made to DB:
create table `tabLMS Program Member` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`full_name` varchar(140),
`progress` int(11) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:22,568 WARNING database DDL Query made to DB:
create table `tabLMS Timetable Legend` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`label` varchar(140),
`color` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:22,664 WARNING database DDL Query made to DB:
create table `tabLMS Certificate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`evaluator` varchar(140),
`evaluator_name` varchar(140),
`issue_date` date,
`expiry_date` date,
`template` varchar(140),
`published` int(1) not null default 0,
`course` varchar(140),
`course_title` varchar(140),
`batch_name` varchar(140),
`batch_title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:22,766 WARNING database DDL Query made to DB:
create table `tabLMS Quiz Result` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` text,
`question_name` varchar(140),
`answer` text,
`marks` int(11) not null default 0,
`marks_out_of` int(11) not null default 0,
`is_correct` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:22,871 WARNING database DDL Query made to DB:
create table `tabCohort Join Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cohort` varchar(140),
`email` varchar(140),
`subgroup` varchar(140),
`status` varchar(140) default 'Pending',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:22,969 WARNING database DDL Query made to DB:
create table `tabLMS Mentor Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`course` varchar(140),
`reviewed_by` varchar(140),
`member_name` varchar(140),
`status` varchar(140),
`comments` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:23,114 WARNING database DDL Query made to DB:
create table `tabCohort Web Page` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`slug` varchar(140),
`title` varchar(140),
`template` varchar(140),
`scope` varchar(140) default 'Cohort',
`required_role` varchar(140) default 'Public',
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:23,317 WARNING database DDL Query made to DB:
create table `tabCohort Staff` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cohort` varchar(140),
`course` varchar(140),
`email` varchar(140),
`role` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:23,417 WARNING database DDL Query made to DB:
create table `tabLMS Exercise` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`description` text,
`code` longtext,
`answer` longtext,
`course` varchar(140),
`hints` text,
`tests` longtext,
`image` longtext,
`lesson` varchar(140),
`index_` int(11) not null default 0,
`index_label` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:23,587 WARNING database DDL Query made to DB:
create table `tabLMS Program Course` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`course_title` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:23,705 WARNING database DDL Query made to DB:
create table `tabLMS Course Progress` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`status` varchar(140),
`lesson` varchar(140),
`chapter` varchar(140),
`course` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `member`(`member`),
index `status`(`status`),
index `lesson`(`lesson`),
index `chapter`(`chapter`),
index `course`(`course`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:23,861 WARNING database DDL Query made to DB:
create table `tabPayment Country` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`country` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:23,943 WARNING database DDL Query made to DB:
create table `tabLMS Timetable Template` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:24,051 WARNING database DDL Query made to DB:
create table `tabLMS Quiz Question` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`question` varchar(140),
`marks` int(11) not null default 1,
`question_detail` text,
`type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:24,154 WARNING database DDL Query made to DB:
create table `tabLMS Certificate Evaluation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`course` varchar(140),
`batch_name` varchar(140),
`evaluator` varchar(140),
`evaluator_name` varchar(140),
`date` date,
`start_time` time(6),
`end_time` time(6),
`rating` decimal(3,2),
`status` varchar(140),
`summary` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:24,303 WARNING database DDL Query made to DB:
create table `tabLMS Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`question` longtext,
`type` varchar(140),
`grade_assignment` int(1) not null default 1,
`show_answer` int(1) not null default 0,
`answer` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:24,455 WARNING database DDL Query made to DB:
create table `tabLMS Program` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:24,602 WARNING database DDL Query made to DB:
create table `tabWork Experience` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`company` varchar(140),
`location` varchar(140),
`description` text,
`current` int(1) not null default 0,
`from_date` date,
`to_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:24,684 WARNING database DDL Query made to DB:
create sequence if not exists evaluator_schedule_id_seq nocache nocycle
2025-05-26 09:00:24,703 WARNING database DDL Query made to DB:
create table `tabEvaluator Schedule` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`day` varchar(140),
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:24,808 WARNING database DDL Query made to DB:
create table `tabLMS Quiz` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140) unique,
`max_attempts` int(11) not null default 0,
`show_answers` int(1) not null default 1,
`show_submission_history` int(1) not null default 0,
`total_marks` int(11) not null default 0,
`passing_percentage` int(11) not null default 0,
`duration` varchar(140),
`shuffle_questions` int(1) not null default 0,
`limit_questions_to` int(11) not null default 0,
`lesson` varchar(140),
`course` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:24,956 WARNING database DDL Query made to DB:
create table `tabCohort` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`title` varchar(140),
`slug` varchar(140) unique,
`instructor` varchar(140),
`status` varchar(140),
`begin_date` date,
`end_date` date,
`duration` varchar(140),
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:25,103 WARNING database DDL Query made to DB:
create table `tabLMS Batch Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`member_image` text,
`batch` varchar(140),
`content` decimal(3,2),
`instructors` decimal(3,2),
`value` decimal(3,2),
`feedback` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:25,209 WARNING database DDL Query made to DB:
create table `tabLMS Course Mentor Mapping` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`mentor` varchar(140),
`mentor_name` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:25,369 WARNING database DDL Query made to DB:
create table `tabCourse Evaluator` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`evaluator` varchar(140) unique,
`full_name` varchar(140),
`user_image` text,
`username` varchar(140),
`unavailable_from` date,
`unavailable_to` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:25,520 WARNING database DDL Query made to DB:
create table `tabChapter Reference` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`chapter` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:25,616 WARNING database DDL Query made to DB:
create table `tabCohort Subgroup` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`cohort` varchar(140),
`slug` varchar(140),
`title` varchar(140),
`invite_code` varchar(140),
`course` varchar(140),
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:25,705 WARNING database DDL Query made to DB:
create table `tabPreferred Function` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`function` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:25,795 WARNING database DDL Query made to DB:
create table `tabUser Skill` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:25,924 WARNING database DDL Query made to DB:
create table `tabScheduled Flow` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`lesson` varchar(140),
`lesson_title` varchar(140),
`date` date,
`start_time` time(6),
`end_time` time(6),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:26,063 WARNING database DDL Query made to DB:
create table `tabRelated Courses` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:26,153 WARNING database DDL Query made to DB:
create table `tabLMS Badge Assignment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`member` varchar(140),
`member_name` varchar(140),
`issued_on` date,
`badge` varchar(140),
`badge_image` text,
`badge_description` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:26,288 WARNING database DDL Query made to DB:
create table `tabExercise Latest Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exercise` varchar(140),
`status` varchar(140),
`batch_old` varchar(140),
`exercise_title` varchar(140),
`course` varchar(140),
`lesson` varchar(140),
`solution` longtext,
`image` longtext,
`test_results` text,
`comments` text,
`latest_submission` varchar(140),
`member` varchar(140),
`member_email` varchar(140),
`member_cohort` varchar(140),
`member_subgroup` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `exercise`(`exercise`),
index `member_email`(`member_email`),
index `member_cohort`(`member_cohort`),
index `member_subgroup`(`member_subgroup`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:26,381 WARNING database DDL Query made to DB:
create table `tabLMS Course Review` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`rating` decimal(3,2),
`course` varchar(140),
`review` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:26,499 WARNING database DDL Query made to DB:
create table `tabExercise Submission` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`exercise` varchar(140),
`status` varchar(140),
`batch_old` varchar(140),
`exercise_title` varchar(140),
`course` varchar(140),
`lesson` varchar(140),
`solution` longtext,
`image` longtext,
`test_results` text,
`comments` text,
`member` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:26,625 WARNING database DDL Query made to DB:
create table `tabSkills` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`skill_name` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:26,733 WARNING database DDL Query made to DB:
create table `tabLMS Option` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`option` varchar(140),
`is_correct` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:26,869 WARNING database DDL Query made to DB:
create table `tabLMS Batch` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`start_date` date,
`end_date` date,
`start_time` time(6),
`end_time` time(6),
`timezone` varchar(140),
`published` int(1) not null default 0,
`allow_self_enrollment` int(1) not null default 0,
`certification` int(1) not null default 0,
`description` text,
`medium` varchar(140) default 'Online',
`category` varchar(140),
`confirmation_email_template` varchar(140),
`seat_count` int(11) not null default 0,
`evaluation_end_date` date,
`meta_image` text,
`batch_details` longtext,
`batch_details_raw` longtext,
`timetable_template` varchar(140),
`show_live_class` int(1) not null default 0,
`allow_future` int(1) not null default 1,
`paid_batch` int(1) not null default 0,
`amount` decimal(21,9) not null default 0,
`currency` varchar(140),
`amount_usd` decimal(21,9) not null default 0,
`custom_component` longtext,
`custom_script` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:26,975 WARNING database DDL Query made to DB:
create sequence if not exists batch_course_id_seq nocache nocycle
2025-05-26 09:00:26,999 WARNING database DDL Query made to DB:
create table `tabBatch Course` (
			name bigint primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`title` varchar(140),
`evaluator` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:27,117 WARNING database DDL Query made to DB:
create table `tabLMS Course Interest` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`user` varchar(140),
`email_sent` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:27,213 WARNING database DDL Query made to DB:
create table `tabIndustry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`industry` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:27,336 WARNING database DDL Query made to DB:
create table `tabEducation Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`institution_name` varchar(140),
`location` varchar(140),
`degree_type` varchar(140),
`major` varchar(140),
`grade_type` varchar(140),
`grade` varchar(140),
`start_date` date,
`end_date` date,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:27,469 WARNING database DDL Query made to DB:
create table `tabLMS Batch Old` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course` varchar(140),
`start_date` date,
`start_time` time(6),
`title` varchar(140),
`sessions_on` varchar(140),
`end_time` time(6),
`description` longtext,
`visibility` varchar(140) default 'Public',
`membership` varchar(140),
`status` varchar(140) default 'Active',
`stage` varchar(140) default 'Ready',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:27,606 WARNING database DDL Query made to DB:
create table `tabPreferred Industry` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`industry` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:27,702 WARNING database DDL Query made to DB:
create table `tabLMS Batch Timetable` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reference_doctype` varchar(140),
`reference_docname` varchar(140),
`date` date,
`day` int(11) not null default 0,
`start_time` time(6),
`end_time` time(6),
`duration` varchar(140),
`milestone` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:27,808 WARNING database DDL Query made to DB:
create table `tabInvite Request` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`invite_email` varchar(140) unique,
`signup_email` varchar(140),
`status` varchar(140) default 'Pending',
`full_name` varchar(140),
`username` varchar(140),
`invite_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:28,359 WARNING database DDL Query made to DB:
create table `tabLMS Job Application` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140),
`resume` text,
`job` varchar(140),
`job_title` varchar(140),
`company` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:28,467 WARNING database DDL Query made to DB:
create table `tabJob Opportunity` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`job_title` varchar(140),
`location` varchar(140),
`country` varchar(140),
`type` varchar(140) default 'Full Time',
`status` varchar(140) default 'Open',
`disabled` int(1) not null default 0,
`company_name` varchar(140),
`company_website` varchar(140),
`company_logo` text,
`company_email_address` varchar(140),
`description` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:29,673 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `country` varchar(140)
2025-05-26 09:00:29,784 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `verify_terms` int(1) not null default 0
2025-05-26 09:00:29,901 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `user_category` varchar(140)
2025-05-26 09:00:30,045 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `cover_image` text
2025-05-26 09:00:30,167 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `looking_for_job` int(1) not null default 0
2025-05-26 09:00:30,289 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `linkedin` varchar(140)
2025-05-26 09:00:30,428 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `github` varchar(140)
2025-05-26 09:00:30,553 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `medium` varchar(140)
2025-05-26 09:00:30,688 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `profession` varchar(140)
2025-05-26 09:00:30,825 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `hide_private` int(1) not null default 0
2025-05-26 09:00:32,002 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `preferred_location` varchar(140)
2025-05-26 09:00:32,348 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `dream_companies` varchar(140)
2025-05-26 09:00:32,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `attire` varchar(140)
2025-05-26 09:00:32,744 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `collaboration` varchar(140)
2025-05-26 09:00:32,889 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `role` varchar(140)
2025-05-26 09:00:33,109 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `location_preference` varchar(140)
2025-05-26 09:00:33,257 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `time` varchar(140)
2025-05-26 09:00:33,406 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `company_type` varchar(140)
2025-05-26 09:00:33,535 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `headline` varchar(140)
2025-05-26 09:00:33,664 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `city` varchar(140)
2025-05-26 09:00:33,796 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `college` varchar(140)
2025-05-26 09:00:33,921 WARNING database DDL Query made to DB:
ALTER TABLE `tabUser` ADD COLUMN `branch` varchar(140)
2025-05-26 09:00:36,265 WARNING database DDL Query made to DB:
create table `tabWishlist` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:36,387 WARNING database DDL Query made to DB:
create table `tabWishlist Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`website_item` varchar(140),
`web_item_name` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`description` longtext,
`route` text,
`image` text,
`warehouse` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:36,488 WARNING database DDL Query made to DB:
create table `tabHomepage Featured Product` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`item_code` varchar(140),
`item_name` varchar(140),
`description` longtext,
`image` text,
`thumbnail` text,
`route` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:36,735 WARNING database DDL Query made to DB:
create table `tabRecommended Items` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`website_item` varchar(140),
`website_item_name` varchar(140),
`item_code` varchar(140),
`route` text,
`website_item_image` text,
`website_item_thumbnail` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:36,872 WARNING database DDL Query made to DB:
create table `tabWebsite Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`naming_series` varchar(140) default 'WEB-ITM-.####',
`web_item_name` varchar(140),
`route` text,
`has_variants` int(1) not null default 0,
`variant_of` varchar(140),
`published` int(1) not null default 1,
`item_code` varchar(140),
`item_name` varchar(140),
`item_group` varchar(140),
`stock_uom` varchar(140),
`description` longtext,
`brand` varchar(140),
`website_image` text,
`website_image_alt` varchar(140),
`slideshow` varchar(140),
`thumbnail` varchar(140),
`website_warehouse` varchar(140),
`on_backorder` int(1) not null default 0,
`short_description` text,
`web_long_description` longtext,
`show_tabbed_section` int(1) not null default 0,
`ranking` int(11) not null default 0,
`website_content` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `variant_of`(`variant_of`),
index `item_group`(`item_group`),
index `brand`(`brand`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:36,967 WARNING database DDL Query made to DB:
ALTER TABLE `tabWebsite Item`
				ADD INDEX IF NOT EXISTS `route_index`(route(500))
2025-05-26 09:00:37,040 WARNING database DDL Query made to DB:
create table `tabItem Review` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`website_item` varchar(140),
`user` varchar(140),
`customer` varchar(140),
`item` varchar(140),
`published_on` varchar(140),
`review_title` varchar(140),
`rating` decimal(3,2),
`comment` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:37,150 WARNING database DDL Query made to DB:
create table `tabWebsite Offer` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`offer_title` varchar(140),
`offer_subtitle` varchar(140),
`offer_details` longtext,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
