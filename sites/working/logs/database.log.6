2025-05-26 08:59:37,197 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric Log` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`punch` varchar(140),
`user_id` varchar(140),
`uid` varchar(140),
`status` varchar(140),
`timestamp` datetime(6),
`device_id` varchar(140),
`device_ip` varchar(140),
`punch_direction` varchar(140),
`biometric_user` varchar(140),
`biometric_user_name` varchar(140),
`biometric` varchar(140),
`meal_type` varchar(140),
`site_name` varchar(140),
`supervisor` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:37,294 WARNING database DDL Query made to DB:
create table `tabCSF TZ Biometric User` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_id` varchar(140) unique,
`uid` varchar(140),
`erpnext_user` varchar(140),
`user_name` varchar(140),
`user_type` varchar(140),
`amended_from` varchar(140),
`gender` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:37,399 WARNING database DDL Query made to DB:
create table `tabCSF TZ Meal Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`meal_name` varchar(140) unique,
`meal_type` varchar(140),
`start_time` time(6),
`end_time` time(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:37,525 WARNING database DDL Query made to DB:
create table `tabStanbic Setting` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`enabled` int(1) not null default 1,
`currency` varchar(140),
`sftp_url` varchar(140),
`sftp_user` varchar(140),
`private_key` text,
`port` int(11) not null default 0,
`pgp_public_key` text,
`pgp_private_key` text,
`initiating_party_name` varchar(140),
`customerid` varchar(140),
`payment_type` varchar(140),
`user` varchar(140),
`ordering_customer_account_number` varchar(140),
`ordering_account_type` varchar(140),
`ordering_account_currency` varchar(140),
`ordering_bank_bic` varchar(140),
`ordering_bank_sort_code` varchar(140),
`ordering_bank_country` varchar(140),
`ordering_bank_country_code` varchar(140),
`charges_bearer` varchar(140),
`file_code` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:37,627 WARNING database DDL Query made to DB:
create table `tabStanbic Payments Info` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`salary_slip` varchar(140),
`employee` varchar(140),
`transfer_currency` varchar(140),
`transfer_amount` decimal(21,9) not null default 0,
`beneficiary_bank_bic` varchar(140),
`beneficiary_bank_sort_code` varchar(140),
`beneficiary_bank_name` varchar(140),
`beneficiary_bank_country_code` varchar(140),
`beneficiary_name` varchar(140),
`beneficiary_country` varchar(140),
`beneficiary_address` varchar(140),
`beneficiary_iban` varchar(140),
`beneficiary_account_number` varchar(140),
`beneficiary_account_type` int(11) not null default 0,
`beneficiary_account_currency` varchar(140),
`stanbic_finaud_status` varchar(140),
`stanbic_intaud_status` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:37,753 WARNING database DDL Query made to DB:
create table `tabStanbic Payments Initiation` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payroll_entry` varchar(140),
`file_code` varchar(140),
`amended_from` varchar(140),
`posting_date` date,
`posting_time` time(6),
`number_of_transactions` int(11) not null default 0,
`control_sum` decimal(21,9) not null default 0,
`stanbic_setting` varchar(140),
`initiating_party_name` varchar(140),
`customer_id` varchar(140),
`ordering_customer_name` varchar(140),
`ordering_customer_account_country` varchar(140) default 'TZ',
`ordering_customer_account_number` varchar(140),
`ordering_account_type` varchar(140),
`ordering_account_currency` varchar(140),
`ordering_bank_bic` varchar(140),
`ordering_bank_sort_code` varchar(140),
`ordering_bank_country_code` varchar(140),
`charges_bearer` varchar(140) default 'DEBT',
`xml` longtext,
`encrypted_xml` longtext,
`stanbic_ack` longtext,
`stanbic_intaud` longtext,
`stanbic_finaud` longtext,
`stanbic_ack_status` varchar(140),
`stanbic_ack_change` int(1) not null default 0,
`stanbic_intaud_change` int(1) not null default 0,
`stanbic_finaud_change` int(1) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:45,986 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` ADD COLUMN `posting_date` date, ADD COLUMN `default_item_discount` decimal(21,9) not null default 0
2025-05-26 08:59:46,009 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Order` MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_picked` decimal(21,9) not null default 0, MODIFY `per_delivered` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-05-26 08:59:46,106 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` ADD COLUMN `expense_record` varchar(140), ADD COLUMN `import_file` varchar(140), ADD COLUMN `referance_doctype` varchar(140), ADD COLUMN `referance_docname` varchar(140), ADD COLUMN `from_date` date, ADD COLUMN `to_date` date
2025-05-26 08:59:46,127 WARNING database DDL Query made to DB:
ALTER TABLE `tabJournal Entry` MODIFY `total_debit` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `difference` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `total_credit` decimal(21,9) not null default 0
2025-05-26 08:59:46,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` ADD COLUMN `import_file` varchar(140)
2025-05-26 08:59:46,208 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Voucher` MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0
2025-05-26 08:59:46,287 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` ADD COLUMN `item` varchar(140)
2025-05-26 08:59:46,310 WARNING database DDL Query made to DB:
ALTER TABLE `tabAccount` MODIFY `tax_rate` decimal(21,9) not null default 0
2025-05-26 08:59:46,396 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `vrn` varchar(140)
2025-05-26 08:59:46,420 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-05-26 08:59:46,503 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `old_employee_id` varchar(140), ADD COLUMN `heslb_f4_index_number` varchar(140)
2025-05-26 08:59:46,529 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-05-26 08:59:46,587 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` ADD COLUMN `image` text
2025-05-26 08:59:46,610 WARNING database DDL Query made to DB:
ALTER TABLE `tabOperation` MODIFY `total_operation_time` decimal(21,9) not null default 0
2025-05-26 08:59:46,707 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `is_ignored_in_pending_qty` int(1) not null default 0, ADD COLUMN `allow_over_sell` int(1) not null default 0, ADD COLUMN `withholding_tax_rate` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_entry` varchar(140), ADD COLUMN `allow_override_net_rate` int(1) not null default 0, ADD COLUMN `delivery_status` varchar(140)
2025-05-26 08:59:46,732 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0
2025-05-26 08:59:46,769 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD INDEX `creation`(`creation`)
2025-05-26 08:59:46,854 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` ADD COLUMN `material_request` varchar(140)
2025-05-26 08:59:46,876 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `current_amount` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `current_valuation_rate` decimal(21,9) not null default 0, MODIFY `amount_difference` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0
2025-05-26 08:59:46,965 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `withholding_tax_rate` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_entry` varchar(140)
2025-05-26 08:59:46,987 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0
2025-05-26 08:59:47,040 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` ADD COLUMN `spare_name` varchar(140), ADD COLUMN `quantity` decimal(21,9) not null default 0, ADD COLUMN `invoice` varchar(140)
2025-05-26 08:59:47,063 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Service` MODIFY `expense_amount` decimal(21,9) not null default 0
2025-05-26 08:59:47,157 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` ADD COLUMN `witholding_tax_rate_on_purchase` decimal(21,9) not null default 0, ADD COLUMN `withholding_tax_rate_on_sales` decimal(21,9) not null default 0, ADD COLUMN `excisable_item` int(1) not null default 0, ADD COLUMN `default_tax_template` varchar(140)
2025-05-26 08:59:47,181 WARNING database DDL Query made to DB:
ALTER TABLE `tabItem` MODIFY `over_billing_allowance` decimal(21,9) not null default 0, MODIFY `standard_rate` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `over_delivery_receipt_allowance` decimal(21,9) not null default 0, MODIFY `total_projected_qty` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `max_discount` decimal(21,9) not null default 0, MODIFY `safety_stock` decimal(21,9) not null default 0, MODIFY `last_purchase_rate` decimal(21,9) not null default 0, MODIFY `opening_stock` decimal(21,9) not null default 0
2025-05-26 08:59:47,285 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` ADD COLUMN `form_sales_invoice` varchar(140)
2025-05-26 08:59:47,308 WARNING database DDL Query made to DB:
ALTER TABLE `tabDelivery Note` MODIFY `per_installed` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `per_returned` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0
2025-05-26 08:59:47,380 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` ADD COLUMN `posting_date` date, ADD COLUMN `start_date` date, ADD COLUMN `end_date` date
2025-05-26 08:59:47,402 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry Reference` MODIFY `payment_term_outstanding` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `exchange_gain_loss` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0, MODIFY `allocated_amount` decimal(21,9) not null default 0
2025-05-26 08:59:47,459 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustom DocPerm` ADD COLUMN `dependent` int(1) not null default 0
2025-05-26 08:59:47,559 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `previous_invoice_number` varchar(140), ADD COLUMN `default_item_discount` decimal(21,9) not null default 0, ADD COLUMN `default_item_tax_template` varchar(140), ADD COLUMN `price_reduction` decimal(21,9) not null default 0, ADD COLUMN `tra_control_number` varchar(140), ADD COLUMN `witholding_tax_certificate_number` varchar(140), ADD COLUMN `electronic_fiscal_device` varchar(140), ADD COLUMN `efd_z_report` varchar(140), ADD COLUMN `excise_duty_applicable` int(1) not null default 0, ADD COLUMN `enabled_auto_create_delivery_notes` int(1) not null default 1, ADD COLUMN `delivery_status` varchar(140)
2025-05-26 08:59:47,581 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0
2025-05-26 08:59:47,636 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD COLUMN `electronic_fiscal_device` varchar(140)
2025-05-26 08:59:47,669 WARNING database DDL Query made to DB:
ALTER TABLE `tabPOS Profile` ADD INDEX `creation`(`creation`)
2025-05-26 08:59:47,738 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` ADD COLUMN `repack_template` varchar(140), ADD COLUMN `item_uom` varchar(140), ADD COLUMN `repack_qty` decimal(21,9) not null default 0, ADD COLUMN `final_destination` varchar(140), ADD COLUMN `total_net_weight` decimal(21,9) not null default 0, ADD COLUMN `transporter` varchar(140), ADD COLUMN `driver` varchar(140), ADD COLUMN `transport_receipt_no` varchar(140), ADD COLUMN `vehicle_no` varchar(140), ADD COLUMN `transporter_name` varchar(140), ADD COLUMN `driver_name` varchar(140), ADD COLUMN `transport_receipt_date` date
2025-05-26 08:59:47,762 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry` MODIFY `total_additional_costs` decimal(21,9) not null default 0, MODIFY `value_difference` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `total_incoming_value` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_outgoing_value` decimal(21,9) not null default 0, MODIFY `per_transferred` decimal(21,9) not null default 0, MODIFY `fg_completed_qty` decimal(21,9) not null default 0, MODIFY `total_amount` decimal(21,9) not null default 0
2025-05-26 08:59:47,841 WARNING database DDL Query made to DB:
ALTER TABLE `tabSupplier` ADD COLUMN `vrn` varchar(140)
2025-05-26 08:59:47,939 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `company_bank_details` text, ADD COLUMN `vrn` varchar(140), ADD COLUMN `tin` varchar(140), ADD COLUMN `p_o_box` varchar(140), ADD COLUMN `city` varchar(140), ADD COLUMN `plot_number` varchar(140), ADD COLUMN `block_number` varchar(140), ADD COLUMN `street` varchar(140), ADD COLUMN `max_records_in_dialog` int(11) not null default 0, ADD COLUMN `default_withholding_payable_account` varchar(140), ADD COLUMN `auto_create_for_purchase_withholding` int(1) not null default 0, ADD COLUMN `auto_submit_for_purchase_withholding` int(1) not null default 0, ADD COLUMN `default_withholding_receivable_account` varchar(140), ADD COLUMN `auto_create_for_sales_withholding` int(1) not null default 0, ADD COLUMN `auto_submit_for_sales_withholding` int(1) not null default 0, ADD COLUMN `bypass_material_request_validation` int(1) not null default 0, ADD COLUMN `default_item_tax_template` varchar(140), ADD COLUMN `enabled_auto_create_delivery_notes` int(1) not null default 1
2025-05-26 08:59:47,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0
2025-05-26 08:59:48,056 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` ADD COLUMN `stock_reconciliation` varchar(140)
2025-05-26 08:59:48,080 WARNING database DDL Query made to DB:
ALTER TABLE `tabMaterial Request Item` MODIFY `qty` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `ordered_qty` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `min_order_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `projected_qty` decimal(21,9) not null default 0
2025-05-26 08:59:48,154 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` ADD COLUMN `weight_per_unit` decimal(21,9) not null default 0, ADD COLUMN `total_weight` decimal(21,9) not null default 0, ADD COLUMN `weight_uom` varchar(140)
2025-05-26 08:59:48,179 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Entry Detail` MODIFY `basic_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `transfer_qty` decimal(21,9) not null default 0, MODIFY `transferred_qty` decimal(21,9) not null default 0, MODIFY `basic_rate` decimal(21,9) not null default 0, MODIFY `additional_cost` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0
2025-05-26 08:59:48,265 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` ADD COLUMN `source_warehouse` varchar(140), ADD COLUMN `fg_warehouse` varchar(140), ADD COLUMN `wip_warehouse` varchar(140), ADD COLUMN `scrap_warehouse` varchar(140)
2025-05-26 08:59:48,289 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0
2025-05-26 08:59:48,349 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` ADD COLUMN `trip_destination` varchar(140), ADD COLUMN `destination_description` varchar(140)
2025-05-26 08:59:48,372 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Log` MODIFY `price` decimal(21,9) not null default 0, MODIFY `fuel_qty` decimal(21,9) not null default 0
2025-05-26 08:59:48,431 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` ADD COLUMN `csf_tz_year` int(11) not null default 0, ADD COLUMN `csf_tz_acquisition_odometer` int(11) not null default 0, ADD COLUMN `csf_tz_engine_number` varchar(140)
2025-05-26 08:59:48,456 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle` MODIFY `vehicle_value` decimal(21,9) not null default 0, MODIFY `uom` varchar(140)
2025-05-26 08:59:48,543 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` ADD COLUMN `fully_paid` int(1) not null default 0
2025-05-26 08:59:48,566 WARNING database DDL Query made to DB:
ALTER TABLE `tabVehicle Fine Record` MODIFY `total` decimal(21,9) not null default 0, MODIFY `penalty` decimal(21,9) not null default 0, MODIFY `charge` decimal(21,9) not null default 0
2025-05-26 08:59:48,658 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` ADD COLUMN `expense_record` varchar(140), ADD COLUMN `import_file` varchar(140)
2025-05-26 08:59:48,681 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice` MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0
2025-05-26 08:59:48,751 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` ADD COLUMN `posting_date` date
2025-05-26 08:59:48,775 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Order` MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_tax_withholding_net_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `advance_paid` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `per_received` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_added` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `taxes_and_charges_deducted` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `per_billed` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0
2025-05-26 08:59:48,838 WARNING database DDL Query made to DB:
ALTER TABLE `tabStock Reconciliation` MODIFY `difference_amount` decimal(21,9) not null default 0
2025-05-26 08:59:49,411 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-05-26 08:59:49,435 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice Item` MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `conversion_factor` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0, MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `company_total_stock` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `actual_batch_qty` decimal(21,9) not null default 0, MODIFY `actual_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `delivered_qty` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `incoming_rate` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0
2025-05-26 08:59:49,505 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` ADD COLUMN `csf_tz_wtax_jv_created` int(1) not null default 0
2025-05-26 08:59:49,530 WARNING database DDL Query made to DB:
ALTER TABLE `tabPurchase Invoice Item` MODIFY `price_list_rate` decimal(21,9) not null default 0, MODIFY `net_rate` decimal(21,9) not null default 0, MODIFY `net_amount` decimal(21,9) not null default 0, MODIFY `withholding_tax_rate` decimal(21,9) not null default 0, MODIFY `base_net_rate` decimal(21,9) not null default 0, MODIFY `weight_per_unit` decimal(21,9) not null default 0, MODIFY `base_price_list_rate` decimal(21,9) not null default 0, MODIFY `margin_rate_or_amount` decimal(21,9) not null default 0, MODIFY `base_net_amount` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `base_rate` decimal(21,9) not null default 0, MODIFY `total_weight` decimal(21,9) not null default 0, MODIFY `rate_with_margin` decimal(21,9) not null default 0, MODIFY `valuation_rate` decimal(21,9) not null default 0, MODIFY `rm_supp_cost` decimal(21,9) not null default 0, MODIFY `base_amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `sales_incoming_rate` decimal(21,9) not null default 0, MODIFY `received_qty` decimal(21,9) not null default 0, MODIFY `discount_percentage` decimal(21,9) not null default 0, MODIFY `item_tax_amount` decimal(21,9) not null default 0, MODIFY `landed_cost_voucher_amount` decimal(21,9) not null default 0, MODIFY `rejected_qty` decimal(21,9) not null default 0, MODIFY `distributed_discount_amount` decimal(21,9) not null default 0, MODIFY `stock_uom_rate` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `base_rate_with_margin` decimal(21,9) not null default 0, MODIFY `rate` decimal(21,9) not null default 0, MODIFY `stock_qty` decimal(21,9) not null default 0
2025-05-26 08:59:49,756 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `csf_tz_is_auto_close_dn` int(1) not null default 0, ADD COLUMN `csf_tz_close_dn_after` int(11) not null default 0
2025-05-26 08:59:49,779 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-05-26 08:59:50,227 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD COLUMN `based_on_hourly_rate` int(1) not null default 0, ADD COLUMN `hourly_rate` decimal(21,9) not null default 0, ADD COLUMN `no_of_hours` decimal(21,9) not null default 0, ADD COLUMN `auto_repeat_frequency` varchar(140), ADD COLUMN `auto_repeat_end_date` date, ADD COLUMN `last_transaction_amount` decimal(21,9) not null default 0, ADD COLUMN `last_transaction_date` date, ADD COLUMN `auto_created_based_on` varchar(140)
2025-05-26 08:59:50,250 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` MODIFY `amount` decimal(21,9) not null default 0
2025-05-26 08:59:50,283 WARNING database DDL Query made to DB:
ALTER TABLE `tabAdditional Salary` ADD INDEX `creation`(`creation`)
2025-05-26 08:59:50,932 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` ADD COLUMN `is_authotp_applied` int(1) not null default 0, ADD COLUMN `default_authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-05-26 08:59:50,963 WARNING database DDL Query made to DB:
ALTER TABLE `tabCustomer` MODIFY `default_commission_rate` decimal(21,9) not null default 0
2025-05-26 08:59:51,072 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` ADD COLUMN `authotp_method` varchar(140), ADD COLUMN `authotp_validated` int(1) not null default 0
2025-05-26 08:59:51,095 WARNING database DDL Query made to DB:
ALTER TABLE `tabSales Invoice` MODIFY `rounding_adjustment` decimal(21,9) not null default 0, MODIFY `discount_amount` decimal(21,9) not null default 0, MODIFY `total_billing_hours` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `base_total` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `default_item_discount` decimal(21,9) not null default 0, MODIFY `price_reduction` decimal(21,9) not null default 0, MODIFY `loyalty_amount` decimal(21,9) not null default 0, MODIFY `total_commission` decimal(21,9) not null default 0, MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `commission_rate` decimal(21,9) not null default 0, MODIFY `base_write_off_amount` decimal(21,9) not null default 0, MODIFY `conversion_rate` decimal(21,9) not null default 0, MODIFY `amount_eligible_for_commission` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `total_net_weight` decimal(21,9) not null default 0, MODIFY `base_net_total` decimal(21,9) not null default 0, MODIFY `outstanding_amount` decimal(21,9) not null default 0, MODIFY `total_advance` decimal(21,9) not null default 0, MODIFY `additional_discount_percentage` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `grand_total` decimal(21,9) not null default 0, MODIFY `change_amount` decimal(21,9) not null default 0, MODIFY `base_grand_total` decimal(21,9) not null default 0, MODIFY `total_qty` decimal(21,9) not null default 0, MODIFY `total` decimal(21,9) not null default 0, MODIFY `net_total` decimal(21,9) not null default 0, MODIFY `base_discount_amount` decimal(21,9) not null default 0, MODIFY `base_change_amount` decimal(21,9) not null default 0, MODIFY `write_off_amount` decimal(21,9) not null default 0, MODIFY `base_rounding_adjustment` decimal(21,9) not null default 0
2025-05-26 08:59:51,318 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-05-26 08:59:51,343 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` MODIFY `leave_without_pay` decimal(21,9) not null default 0, MODIFY `base_total_deduction` decimal(21,9) not null default 0, MODIFY `net_pay` decimal(21,9) not null default 0, MODIFY `unmarked_days` decimal(21,9) not null default 0, MODIFY `base_hour_rate` decimal(21,9) not null default 0, MODIFY `base_rounded_total` decimal(21,9) not null default 0, MODIFY `income_from_other_sources` decimal(21,9) not null default 0, MODIFY `absent_days` decimal(21,9) not null default 0, MODIFY `deductions_before_tax_calculation` decimal(21,9) not null default 0, MODIFY `total_working_hours` decimal(21,9) not null default 0, MODIFY `ctc` decimal(21,9) not null default 0, MODIFY `income_tax_deducted_till_date` decimal(21,9) not null default 0, MODIFY `hour_rate` decimal(21,9) not null default 0, MODIFY `tax_exemption_declaration` decimal(21,9) not null default 0, MODIFY `base_gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_net_pay` decimal(21,9) not null default 0, MODIFY `month_to_date` decimal(21,9) not null default 0, MODIFY `total_deduction` decimal(21,9) not null default 0, MODIFY `total_income_tax` decimal(21,9) not null default 0, MODIFY `future_income_tax_deductions` decimal(21,9) not null default 0, MODIFY `total_earnings` decimal(21,9) not null default 0, MODIFY `annual_taxable_amount` decimal(21,9) not null default 0, MODIFY `current_month_income_tax` decimal(21,9) not null default 0, MODIFY `payment_days` decimal(21,9) not null default 0, MODIFY `gross_pay` decimal(21,9) not null default 0, MODIFY `year_to_date` decimal(21,9) not null default 0, MODIFY `base_gross_pay` decimal(21,9) not null default 0, MODIFY `rounded_total` decimal(21,9) not null default 0, MODIFY `non_taxable_earnings` decimal(21,9) not null default 0, MODIFY `standard_tax_exemption_amount` decimal(21,9) not null default 0, MODIFY `base_month_to_date` decimal(21,9) not null default 0, MODIFY `total_working_days` decimal(21,9) not null default 0, MODIFY `gross_year_to_date` decimal(21,9) not null default 0, MODIFY `base_year_to_date` decimal(21,9) not null default 0
2025-05-26 08:59:51,379 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Slip` ADD INDEX `creation`(`creation`)
2025-05-26 08:59:51,450 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD COLUMN `has_payroll_approval` int(1) not null default 0
2025-05-26 08:59:51,474 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-05-26 08:59:51,507 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayroll Entry` ADD INDEX `creation`(`creation`)
2025-05-26 08:59:51,950 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` ADD COLUMN `employee_country` varchar(140), ADD COLUMN `employee_country_code` varchar(140), ADD COLUMN `beneficiary_bank_bic` varchar(140), ADD COLUMN `bank_country_code` varchar(140), ADD COLUMN `bank_country` varchar(140), ADD COLUMN `bank_account_name` varchar(140)
2025-05-26 08:59:51,976 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-05-26 08:59:52,107 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD COLUMN `allow_negative` int(1) not null default 0
2025-05-26 08:59:52,129 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` MODIFY `amount` decimal(21,9) not null default 0, MODIFY `max_benefit_amount` decimal(21,9) not null default 0
2025-05-26 08:59:52,161 WARNING database DDL Query made to DB:
ALTER TABLE `tabSalary Component` ADD INDEX `creation`(`creation`)
2025-05-26 08:59:52,618 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` ADD COLUMN `bank_charges` decimal(21,9) not null default 0, ADD COLUMN `bank_charges_journal_entry` varchar(140), ADD COLUMN `bank_charges_description` text
2025-05-26 08:59:52,643 WARNING database DDL Query made to DB:
ALTER TABLE `tabPayment Entry` MODIFY `total_allocated_amount` decimal(21,9) not null default 0, MODIFY `total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `party_balance` decimal(21,9) not null default 0, MODIFY `base_received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_received_amount` decimal(21,9) not null default 0, MODIFY `unallocated_amount` decimal(21,9) not null default 0, MODIFY `source_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_to_account_balance` decimal(21,9) not null default 0, MODIFY `paid_from_account_balance` decimal(21,9) not null default 0, MODIFY `base_paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_taxes_and_charges` decimal(21,9) not null default 0, MODIFY `paid_amount_after_tax` decimal(21,9) not null default 0, MODIFY `base_total_allocated_amount` decimal(21,9) not null default 0, MODIFY `received_amount` decimal(21,9) not null default 0, MODIFY `base_paid_amount` decimal(21,9) not null default 0, MODIFY `received_amount_after_tax` decimal(21,9) not null default 0, MODIFY `difference_amount` decimal(21,9) not null default 0, MODIFY `target_exchange_rate` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0
2025-05-26 08:59:52,748 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `default_bank_charges_account` varchar(140)
2025-05-26 08:59:52,772 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-05-26 08:59:52,890 WARNING database DDL Query made to DB:
ALTER TABLE `tabBOM` MODIFY `plc_conversion_rate` decimal(21,9) not null default 0, MODIFY `operating_cost` decimal(21,9) not null default 0, MODIFY `base_raw_material_cost` decimal(21,9) not null default 0, MODIFY `base_scrap_material_cost` decimal(21,9) not null default 0, MODIFY `operating_cost_per_bom_quantity` decimal(21,9) not null default 0, MODIFY `total_cost` decimal(21,9) not null default 0, MODIFY `process_loss_qty` decimal(21,9) not null default 0, MODIFY `process_loss_percentage` decimal(21,9) not null default 0, MODIFY `raw_material_cost` decimal(21,9) not null default 0, MODIFY `scrap_material_cost` decimal(21,9) not null default 0, MODIFY `base_operating_cost` decimal(21,9) not null default 0, MODIFY `base_total_cost` decimal(21,9) not null default 0
2025-05-26 08:59:53,185 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `max_unclaimed_ea` int(11) not null default 0
2025-05-26 08:59:53,212 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-05-26 08:59:53,298 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD COLUMN `employee_advance_ref` varchar(140), ADD COLUMN `total_travel_cost` decimal(21,9) not null default 0
2025-05-26 08:59:53,332 WARNING database DDL Query made to DB:
ALTER TABLE `tabTravel Request` ADD INDEX `creation`(`creation`)
2025-05-26 08:59:53,415 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD COLUMN `travel_request_ref` varchar(140)
2025-05-26 08:59:53,437 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` MODIFY `pending_amount` decimal(21,9) not null default 0, MODIFY `paid_amount` decimal(21,9) not null default 0, MODIFY `claimed_amount` decimal(21,9) not null default 0, MODIFY `return_amount` decimal(21,9) not null default 0, MODIFY `advance_amount` decimal(21,9) not null default 0, MODIFY `exchange_rate` decimal(21,9) not null default 0
2025-05-26 08:59:53,469 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee Advance` ADD INDEX `creation`(`creation`)
2025-05-26 08:59:53,607 WARNING database DDL Query made to DB:
ALTER TABLE `tabEmployee` MODIFY `ctc` decimal(21,9) not null default 0
2025-05-26 08:59:53,866 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` ADD COLUMN `enable_auto_close_material_request` int(1) not null default 0, ADD COLUMN `close_material_request_after` int(11) not null default 0
2025-05-26 08:59:53,892 WARNING database DDL Query made to DB:
ALTER TABLE `tabCompany` MODIFY `monthly_sales_target` decimal(21,9) not null default 0, MODIFY `total_monthly_sales` decimal(21,9) not null default 0, MODIFY `credit_limit` decimal(21,9) not null default 0
2025-05-26 08:59:54,021 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` ADD COLUMN `applicable_charges_per_item` decimal(21,9) not null default 0, ADD COLUMN `price_per_item` decimal(21,9) not null default 0
2025-05-26 08:59:54,043 WARNING database DDL Query made to DB:
ALTER TABLE `tabLanded Cost Item` MODIFY `rate` decimal(21,9) not null default 0, MODIFY `amount` decimal(21,9) not null default 0, MODIFY `qty` decimal(21,9) not null default 0, MODIFY `applicable_charges` decimal(21,9) not null default 0
2025-05-26 08:59:54,304 WARNING database DDL Query made to DB:
ALTER TABLE `tabBank Account` ADD COLUMN `bank_supplier` varchar(140)
2025-05-26 08:59:55,539 WARNING database DDL Query made to DB:
create table `tabPayment Gateway` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway` varchar(140) unique,
`gateway_settings` varchar(140),
`gateway_controller` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:55,642 WARNING database DDL Query made to DB:
create table `tabBraintree Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway_name` varchar(140),
`merchant_id` varchar(140),
`public_key` varchar(140),
`private_key` text,
`use_sandbox` int(1) not null default 0,
`header_img` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:55,821 WARNING database DDL Query made to DB:
create table `tabGoCardless Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway_name` varchar(140) unique,
`access_token` varchar(140),
`webhooks_secret` varchar(140),
`use_sandbox` int(1) not null default 0,
`header_img` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:55,929 WARNING database DDL Query made to DB:
create table `tabMpesa Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`payment_gateway_name` varchar(140) unique,
`consumer_key` varchar(140),
`consumer_secret` text,
`initiator_name` varchar(140),
`till_number` varchar(140),
`transaction_limit` decimal(21,9) not null default 150000.0,
`sandbox` int(1) not null default 0,
`business_shortcode` varchar(140),
`online_passkey` text,
`security_credential` text,
`account_balance` longtext,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:56,133 WARNING database DDL Query made to DB:
create table `tabGoCardless Mandate` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`disabled` int(1) not null default 0,
`mandate` varchar(140) unique,
`gocardless_customer` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:56,220 WARNING database DDL Query made to DB:
create table `tabStripe Settings` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`gateway_name` varchar(140),
`publishable_key` varchar(140),
`secret_key` text,
`header_img` text,
`redirect_url` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:57,352 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form` ADD COLUMN `currency` varchar(140), ADD COLUMN `amount_field` varchar(140), ADD COLUMN `payment_button_help` text, ADD COLUMN `amount_based_on_field` int(1) not null default 0, ADD COLUMN `amount` decimal(21,9) not null default 0, ADD COLUMN `payment_gateway` varchar(140), ADD COLUMN `payment_button_label` varchar(140) default 'Buy Now', ADD COLUMN `accept_payment` int(1) not null default 0
2025-05-26 08:59:57,374 WARNING database DDL Query made to DB:
ALTER TABLE `tabWeb Form` MODIFY `condition_json` json
2025-05-26 08:59:57,477 WARNING database DDL Query made to DB:
ALTER TABLE `tabGoCardless Mandate` ADD COLUMN `customer` varchar(140)
2025-05-26 08:59:57,824 WARNING database DDL Query made to DB:
create table `tabCourse` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`code` varchar(140),
`title` varchar(140),
`description` text,
`credits` int(11) not null default 0,
`department_id` varchar(140),
`students` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:57,917 WARNING database DDL Query made to DB:
create table `tabStudent` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_id` varchar(140),
`program_id` varchar(140),
`admission_date` date,
`expected_graduation` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:58,047 WARNING database DDL Query made to DB:
create table `tabGrade` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`enrollment_id` varchar(140),
`assessment_id` varchar(140),
`score` decimal(21,9) not null default 0,
`grade` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:58,159 WARNING database DDL Query made to DB:
create table `tabProgramme` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`program_name` varchar(140),
`code` varchar(140),
`department_id` varchar(140),
`type` varchar(140),
`credits_required` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:58,242 WARNING database DDL Query made to DB:
create table `tabEnrollment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`student` varchar(140),
`class_id` varchar(140),
`enrollment_date` date,
`status` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:58,335 WARNING database DDL Query made to DB:
create table `tabTeacher` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`first_name` varchar(140),
`middle_name` varchar(140),
`last_name` varchar(140),
`full_name` varchar(140),
`email` varchar(140) unique,
`phone_number` varchar(140),
`hire_date` date,
`specialization` varchar(140),
`department` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:58,434 WARNING database DDL Query made to DB:
create table `tabBuilding` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`building_name` varchar(140),
`code` varchar(140),
`location` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:58,546 WARNING database DDL Query made to DB:
create table `tabSchedule` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`class` varchar(140),
`day` varchar(140),
`start_time` time(6),
`end_time` time(6),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:58,628 WARNING database DDL Query made to DB:
create table `tabFaculty` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`user_id` varchar(140),
`department_id` varchar(140),
`position` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:58,719 WARNING database DDL Query made to DB:
create table `tabClass` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`course_id` varchar(140),
`faculty_id` varchar(140),
`schedule` varchar(140),
`classroom` varchar(140),
`term_id` varchar(140),
`room_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:58,839 WARNING database DDL Query made to DB:
create table `tabDepartments` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`depart_name` varchar(140),
`head_of_department` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:58,924 WARNING database DDL Query made to DB:
create table `tabAssessment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`class_id` varchar(140),
`title` varchar(140),
`type` varchar(140),
`max_score` varchar(140),
`weight` varchar(140),
`due_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:59,050 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` ADD COLUMN `enrollment_id` varchar(140), ADD COLUMN `date` date
2025-05-26 08:59:59,072 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` MODIFY `status` varchar(140)
2025-05-26 08:59:59,146 WARNING database DDL Query made to DB:
ALTER TABLE `tabAttendance` DROP INDEX `status`
2025-05-26 08:59:59,273 WARNING database DDL Query made to DB:
create table `tabSemester` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`semester_name` varchar(140),
`start_date` date,
`end_date` date,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 08:59:59,361 WARNING database DDL Query made to DB:
create table `tabRoom` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`building_id` varchar(140),
`room_no` varchar(140),
`capacity` varchar(140),
`type` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:00,013 WARNING database DDL Query made to DB:
create table `tabWiki App Switcher List Table` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`app_title` varchar(140),
`wiki_space` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:00,108 WARNING database DDL Query made to DB:
create table `tabWiki Sidebar` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`wiki_page` varchar(140) unique,
`parent_label` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:00,206 WARNING database DDL Query made to DB:
create table `tabWiki Space` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`space_name` varchar(140),
`route` varchar(140) unique,
`app_switcher_logo` text,
`light_mode_logo` text,
`dark_mode_logo` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:00,384 WARNING database DDL Query made to DB:
create table `tabWiki Group Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`parent_label` varchar(140),
`wiki_page` varchar(140),
`hide_on_sidebar` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:00,479 WARNING database DDL Query made to DB:
create table `tabWiki Page` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`title` varchar(140),
`route` varchar(140) unique,
`published` int(1) not null default 0,
`allow_guest` int(1) not null default 1,
`content` longtext default 'No Content',
`meta_description` text,
`meta_image` text,
`meta_keywords` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:00,564 WARNING database DDL Query made to DB:
create table `tabWiki Page Revision Item` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`wiki_page` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:00,646 WARNING database DDL Query made to DB:
create table `tabWiki Feedback` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`wiki_page` varchar(140),
`status` varchar(140) default 'Open',
`rating` decimal(3,2),
`feedback` text,
`email_id` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:00,749 WARNING database DDL Query made to DB:
create table `tabWiki Page Patch` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`wiki_page` varchar(140),
`new_title` varchar(140),
`new_sidebar_group` varchar(140),
`message` text,
`raised_by` varchar(140),
`status` varchar(140) default 'Under Review',
`approved_by` varchar(140),
`new` int(1) not null default 0,
`orignal_code` longtext,
`new_code` longtext,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:00,846 WARNING database DDL Query made to DB:
create table `tabWiki Page Revision` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`content` longtext,
`raised_by` varchar(140),
`raised_by_username` varchar(140),
`message` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:01,981 WARNING database DDL Query made to DB:
create table `tabClearing File Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_document_id` varchar(140),
`document_name` varchar(140),
`document_received` int(1) not null default 0,
`submission_date` datetime(6),
`view_document` text,
`document_attributes` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:02,108 WARNING database DDL Query made to DB:
create table `tabClearing Charges` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_file` varchar(140),
`status` varchar(140),
`consigee` varchar(140),
`tra_clearance_total` decimal(21,9) not null default 0,
`port_clearance_total` decimal(21,9) not null default 0,
`shipment_clearance_total` decimal(21,9) not null default 0,
`physical_clearance_total` decimal(21,9) not null default 0,
`total_charges_sum` decimal(21,9) not null default 0,
`invoice_number` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:02,207 WARNING database DDL Query made to DB:
create table `tabAdditional Expense` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`expense_type` varchar(140),
`expense_amount` decimal(21,9) not null default 0,
`currency` varchar(140),
`expense_date` varchar(140),
`expense_receipt` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:02,337 WARNING database DDL Query made to DB:
create table `tabPhysical Verification` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_file` varchar(140) unique,
`posting_date` date,
`verification_status` varchar(140),
`status` varchar(140),
`consignee` varchar(140),
`consignee_address` varchar(140),
`address_display` text,
`release_order_date` date,
`inspection_date` datetime(6),
`verification_location` varchar(140),
`total_charges` decimal(21,9) not null default 0,
`invoice_paid` int(1) not null default 0,
`paid_by_clearing_agent` int(1) not null default 0,
`staff_id` varchar(140),
`staff_name` varchar(140),
`notes` text,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:02,446 WARNING database DDL Query made to DB:
create table `tabTRA Clearance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_file` varchar(140),
`customer` varchar(140),
`mode_of_transport` varchar(140),
`posting_date` date,
`status` varchar(140),
`total_charges` decimal(21,9) not null default 0,
`paid_by_clearing_agent` int(1) not null default 0,
`invoice_paid` int(1) not null default 0,
`notes` text,
`amended_from` varchar(140),
`clearing_file_reference` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:02,575 WARNING database DDL Query made to DB:
create table `tabShipping Line Clearance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_file` varchar(140),
`consignee` varchar(140),
`delivery_order_expire_date` date,
`drop_off_date` date,
`container_deposit_amount` decimal(21,9) not null default 0,
`posting_date` date,
`status` varchar(140),
`staff_id` varchar(140),
`staff_name` varchar(140),
`container_deposit_returned` int(1) not null default 0,
`total_charges` decimal(21,9) not null default 0,
`paid_by_clearing_agent` int(1) not null default 0,
`invoice_paid` int(1) not null default 0,
`invoice_received` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:02,671 WARNING database DDL Query made to DB:
create table `tabClearing Document Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_type` varchar(140) unique,
`linked_document` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:02,760 WARNING database DDL Query made to DB:
create table `tabClearing Document Type Attribute` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_attribute` varchar(140),
`mandatory` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:02,848 WARNING database DDL Query made to DB:
create table `tabShip clearance Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_document_id` varchar(140),
`document_name` varchar(140),
`document_received` int(1) not null default 0,
`view_document` text,
`document_attributes` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:02,923 WARNING database DDL Query made to DB:
create table `tabContainer Interchange` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_file` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:03,021 WARNING database DDL Query made to DB:
create table `tabDocument Attachments` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140),
`description` varchar(140),
`reference_number` varchar(140),
`issue_date` date,
`expire_date` date,
`attachment` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:03,102 WARNING database DDL Query made to DB:
create table `tabReason for Risk Attribute` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reason_for_risk_attribute` varchar(140),
`reason_for_risk_value` varchar(140),
`assessment_date` date,
`required_actions` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:03,183 WARNING database DDL Query made to DB:
create table `tabPhysical Verification Image` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`attach_image` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:03,273 WARNING database DDL Query made to DB:
create table `tabShipper` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140) unique,
`address` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:03,373 WARNING database DDL Query made to DB:
create table `tabTruck Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`truck_number` varchar(140),
`driver_name` varchar(140),
`driver_license_number` varchar(140),
`driver_contact_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:03,460 WARNING database DDL Query made to DB:
create table `tabRisk Assessment` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`hs_code` varchar(140),
`risk_level` varchar(140),
`assessed_by` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:03,563 WARNING database DDL Query made to DB:
create table `tabTransporter Truck Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`truck_number` varchar(140),
`driver_name` varchar(140),
`driver_license_number` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:03,666 WARNING database DDL Query made to DB:
create table `tabAirline` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140) unique,
`airline_code` varchar(140),
`contact_number` varchar(140),
`email_address` varchar(140),
`address` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:03,763 WARNING database DDL Query made to DB:
create table `tabClearing Charge Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`charge_type` varchar(140),
`description` varchar(140),
`currency` varchar(140),
`amount` decimal(21,9) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:03,850 WARNING database DDL Query made to DB:
create table `tabContainer Location` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`location_name` varchar(140) unique,
`address` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:03,938 WARNING database DDL Query made to DB:
create table `tabDocument Type` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:04,078 WARNING database DDL Query made to DB:
create table `tabClearing File` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`posting_date` varchar(140),
`company` varchar(140),
`status` varchar(140),
`customer` varchar(140),
`address_display` text,
`customer_address` varchar(140),
`clearing_company` varchar(140),
`shipper` varchar(140),
`shipment_type` varchar(140),
`mode_of_transport` varchar(140),
`bl_type` varchar(140),
`carrier_name` varchar(140),
`airline` varchar(140),
`voyage_flight_number` varchar(140),
`airplane` varchar(140),
`departure_date` date,
`arrival_date` date,
`cargo_country_of_origin` varchar(140),
`cargo_description` text,
`cargo_location` varchar(140),
`notes` text,
`special_instructions` text,
`amended_from` varchar(140),
`tancis_lodging_date` date,
`reference_no` varchar(140),
`tansad_no` varchar(140),
`declaration_type` varchar(140),
`cl_plan` varchar(140),
`awbbl_no` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:04,183 WARNING database DDL Query made to DB:
create table `tabClearing Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_file` varchar(140),
`consignee` varchar(140),
`linked_file` varchar(140),
`posting_date` date,
`company` varchar(140),
`document_type` varchar(140),
`document_attachment` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:04,284 WARNING database DDL Query made to DB:
create table `tabAirplane` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140) unique,
`airline` varchar(140),
`aircraft_type` varchar(140),
`flight_number` varchar(140) unique,
`capacity` varchar(140),
`registration_number` varchar(140),
`year_built` int(11) not null default 0,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:04,401 WARNING database DDL Query made to DB:
create table `tabCF Delivery Note` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_file` varchar(140),
`consignee` varchar(140),
`posting_date` date,
`delivery_date` date,
`loading_date` date,
`address` text,
`delivery_location` varchar(140),
`staff_id` varchar(140),
`staff_name` varchar(140),
`exporter_type` varchar(140),
`sub_contract_company` varchar(140),
`has_container_interchange` int(1) not null default 0,
`return_date` date,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:04,494 WARNING database DDL Query made to DB:
create table `tabPhysical Verification Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_document_id` varchar(140),
`document_name` varchar(140),
`document_received` int(1) not null default 0,
`document_attributes` text,
`view_document` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:04,583 WARNING database DDL Query made to DB:
create table `tabClearing Document Attribute` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_attribute` varchar(140),
`document_attribute_value` varchar(140),
`mandatory` int(1) not null default 0,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:04,674 WARNING database DDL Query made to DB:
create table `tabMode of Transport` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`name1` varchar(140) unique,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:04,767 WARNING database DDL Query made to DB:
create table `tabTRA Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_document_id` varchar(140),
`document_name` varchar(140),
`document_received` int(1) not null default 0,
`document_attributes` text,
`view_document` text,
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:04,881 WARNING database DDL Query made to DB:
create table `tabPort Clearance` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_file` varchar(140),
`cf_delivery_note` varchar(140),
`consignee` varchar(140),
`status` varchar(140),
`consignee_address` text,
`staff_id` varchar(140),
`staff_name` varchar(140),
`total_charges` decimal(21,9) not null default 0,
`paid_by_clearing_agent` int(1) not null default 0,
`invoice_paid` int(1) not null default 0,
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:04,965 WARNING database DDL Query made to DB:
create table `tabRisk` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`reason_for_risk` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:05,090 WARNING database DDL Query made to DB:
ALTER TABLE `tabShipping Line` ADD COLUMN `shipping_line` varchar(140), ADD COLUMN `shipping_line_code` varchar(140), ADD COLUMN `contact_number` varchar(140), ADD COLUMN `email_address` varchar(140), ADD COLUMN `address` text
2025-05-26 09:00:05,228 WARNING database DDL Query made to DB:
create table `tabTransporter` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company_name` varchar(140) unique,
`companys_address` text,
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:05,402 WARNING database DDL Query made to DB:
create table `tabMode of Transport Detail` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`clearing_document_type` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:06,223 WARNING database DDL Query made to DB:
create table `tabPort clearance Document` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`document_name` varchar(140),
`document_received` int(1) not null default 0,
`view_document` text,
`document_attributes` text,
`clearing_document_id` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:07,284 WARNING database DDL Query made to DB:
create table `tabCargo` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`package_type` varchar(140),
`hs_code` varchar(140),
`container_number` varchar(140),
`quantity_of_hs_code` int(11) not null default 0,
`quantity_of_container` int(11) not null default 0,
`cargo_description` varchar(140),
`number_of_packages` int(11) not null default 0,
`seal_number` varchar(140),
`weight` decimal(21,9) not null default 0,
`volume` decimal(21,9) not null default 0,
`value` decimal(21,9) not null default 0,
`currency` varchar(140),
`trading_country` varchar(140),
`country_last_consignment` varchar(140),
`country_of_destination` varchar(140),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:08,506 WARNING database DDL Query made to DB:
create table `tabMulti-Dimensional Budget Account` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`account` varchar(140),
`budget_amount` decimal(21,9) not null default 0,
index `account`(`account`),
index `budget_amount`(`budget_amount`),
parent varchar(140),
parentfield varchar(140),
parenttype varchar(140),
index parent(parent))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:08,671 WARNING database DDL Query made to DB:
create table `tabTarget Revenue` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`start_date` date,
`end_date` date,
`branch` varchar(140),
`override_earned_amount` int(1) not null default 0,
`actual_earned_amount` decimal(21,9) not null default 0,
`target_amount` decimal(21,9) not null default 0,
`earned_amount` decimal(21,9) not null default 0,
`target_percentage` decimal(21,9) not null default 0,
`naming_series` varchar(140),
`amended_from` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `start_date`(`start_date`),
index `end_date`(`end_date`),
index `branch`(`branch`),
index `override_earned_amount`(`override_earned_amount`),
index `actual_earned_amount`(`actual_earned_amount`),
index `target_amount`(`target_amount`),
index `earned_amount`(`earned_amount`),
index `target_percentage`(`target_percentage`),
index `naming_series`(`naming_series`),
index `amended_from`(`amended_from`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:08,804 WARNING database DDL Query made to DB:
create table `tabCompany Budget Control` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140) unique,
`frequency` varchar(140),
`applicable_on_material_request` int(1) not null default 0,
`action_if_budget_exceeded_on_mr` varchar(140) default 'Stop',
`applicable_on_purchase_order` int(1) not null default 0,
`action_if_budget_exceeded_on_po` varchar(140) default 'Stop',
`applicable_on_booking_actual_expenses` int(1) not null default 0,
`action_if_budget_exceeded_on_actual` varchar(140) default 'Stop',
`target_calculation_method` varchar(140),
`fixed_target_revenue_amount` decimal(21,9) not null default 0,
`group_income_account` varchar(140),
`action_if_no_target_revenue` varchar(140) default 'Warn',
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
`_seen` text,
index `applicable_on_material_request`(`applicable_on_material_request`),
index `action_if_budget_exceeded_on_mr`(`action_if_budget_exceeded_on_mr`),
index `applicable_on_purchase_order`(`applicable_on_purchase_order`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
2025-05-26 09:00:08,973 WARNING database DDL Query made to DB:
create table `tabMulti-Dimensional Budget` (
			name varchar(140) primary key,
			creation datetime(6),
			modified datetime(6),
			modified_by varchar(140),
			owner varchar(140),
			docstatus int(1) not null default '0',
			idx int(8) not null default '0',
			`company` varchar(140),
`start_date` date,
`end_date` date,
`applicable_on_material_request` int(1) not null default 0,
`action_if_budget_exceeded_on_mr` varchar(140) default 'Stop',
`applicable_on_purchase_order` int(1) not null default 0,
`action_if_budget_exceeded_on_po` varchar(140) default 'Stop',
`applicable_on_booking_actual_expenses` int(1) not null default 0,
`action_if_budget_exceeded` varchar(140) default 'Stop',
`amended_from` varchar(140),
`naming_series` varchar(140),
`_user_tags` text,
`_comments` text,
`_assign` text,
`_liked_by` text,
index `company`(`company`),
index `start_date`(`start_date`),
index `end_date`(`end_date`),
index `applicable_on_material_request`(`applicable_on_material_request`),
index `action_if_budget_exceeded_on_mr`(`action_if_budget_exceeded_on_mr`),
index `applicable_on_purchase_order`(`applicable_on_purchase_order`),
index `action_if_budget_exceeded_on_po`(`action_if_budget_exceeded_on_po`),
index `applicable_on_booking_actual_expenses`(`applicable_on_booking_actual_expenses`),
index `action_if_budget_exceeded`(`action_if_budget_exceeded`),
index `amended_from`(`amended_from`),
index `naming_series`(`naming_series`),
index modified(modified))
			ENGINE=InnoDB
			ROW_FORMAT=DYNAMIC
			CHARACTER SET=utf8mb4
			COLLATE=utf8mb4_unicode_ci
